const { initializeApp, applicationDefault } = require('firebase-admin/app');
const { getFirestore } = require('firebase-admin/firestore');

// Initialize Firebase Admin SDK
initializeApp({
  credential: applicationDefault(),
  // Optionally specify projectId if needed:
  // projectId: 'kingdom2-9d1aa'
});

const db = getFirestore();

async function updateNewsImagePaths() {
  const newsRef = db.collection('news');
  const snapshot = await newsRef.get();
  let updatedCount = 0;

  for (const doc of snapshot.docs) {
    const data = doc.data();
    const updates = {};

    // Update mainImage
    if (data.mainImage && typeof data.mainImage === 'string' && !data.mainImage.startsWith('news/')) {
      const filename = data.mainImage.split('/').pop();
      updates.mainImage = `news/${filename}`;
    }

    // Update featuredImagePath
    if (data.featuredImagePath && typeof data.featuredImagePath === 'string' && !data.featuredImagePath.startsWith('news/')) {
      const filename = data.featuredImagePath.split('/').pop();
      updates.featuredImagePath = `news/${filename}`;
    }

    // Update imagesPaths array
    if (Array.isArray(data.imagesPaths)) {
      updates.imagesPaths = data.imagesPaths.map(path => {
        if (typeof path === 'string' && !path.startsWith('news/')) {
          const filename = path.split('/').pop();
          return `news/${filename}`;
        }
        return path;
      });
    }

    // Only update if there are changes
    if (Object.keys(updates).length > 0) {
      await doc.ref.update(updates);
      updatedCount++;
      console.log(`Updated news doc: ${doc.id}`, updates);
    }
  }

  console.log(`Image path update complete. Updated ${updatedCount} news documents.`);
}

updateNewsImagePaths().catch(console.error);