# --- Firebase Client SDK (REQUIRED for Next.js client-side Firebase) ---
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyA6kOmVJ3fyDFtRtGihelKAvKUYW_zncE8
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=kingdom2-9d1aa.firebaseapp.com
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://kingdom2-9d1aa-default-rtdb.firebaseio.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=kingdom2-9d1aa
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=kingdom2-9d1aa.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:57dadca952729fb69ebd7e
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-CC69WW2V3B

# --- Firebase Admin SDK (REQUIRED for Next.js server-side Firebase) ---
# This must be a BASE64-encoded string of your service account JSON!
FIREBASE_SERVICE_ACCOUNT_KEY=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# --- Application URLs ---
NEXT_PUBLIC_APP_URL=https://adukingdom-62986aa3239a.herokuapp.comrFC87JNm<63U9gN=1

# Email Configuration
EMAIL_PASSWORD= 'q7gpndHOAc80'

# Replace 'your_email_password_here' with the actual <NAME_EMAIL>

# Email Configuration for Adukrom Kingdom
# SMTP Server: s4478.usc1.stableserver.net:465 (SSL/TLS)

# Email Accounts and Passwords
PRESS_EMAIL=<EMAIL>
PRESS_PASSWORD= 'q7gpndHOAc80'

INFO_EMAIL=<EMAIL>
INFO_PASSWORD= 'Olc2Q*)Hg*iv'

LEGAL_EMAIL=<EMAIL>
LEGAL_PASSWORD= '(Q)&pq0IM#*q'

EVENTS_EMAIL=<EMAIL>
EVENTS_PASSWORD= '5aDDh#b?L9r='

MERCH_EMAIL=<EMAIL>
MERCH_PASSWORD= '@z?6@@QhKTw_'

# Default configuration for contact forms
# Contact forms will <NAME_EMAIL> TO <EMAIL>
EMAIL_FROM=<EMAIL>
EMAIL_FROM_PASSWORD= '5aDDh#b?L9r='
EMAIL_TO=<EMAIL>