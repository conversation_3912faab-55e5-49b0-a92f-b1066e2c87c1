const { initializeApp } = require('firebase/app');
const { getStorage, ref, listAll, getDownloadURL } = require('firebase/storage');

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const storage = getStorage(app);

async function listFiles(path = '') {
  try {
    console.log(`Listing files in path: ${path || 'root'}`);
    const listRef = ref(storage, path);
    const res = await listAll(listRef);
    
    console.log(`\nDirectories in ${path || 'root'}:`);
    for (const prefix of res.prefixes) {
      console.log(`- ${prefix.name}/`);
    }
    
    console.log(`\nFiles in ${path || 'root'}:`);
    for (const item of res.items) {
      const url = await getDownloadURL(item);
      console.log(`- ${item.name} (${url})`);
    }
    
  } catch (error) {
    console.error('Error listing files:', error);
  }
}

// List files in the root directory
listFiles().then(() => {
  // List files in the Website Images directory
  return listFiles('Website Images');
}).then(() => {
  // List files in the news directory
  return listFiles('news');
}).then(() => {
  // List files in the Partner Logos directory
  return listFiles('Partner Logos');
}).catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
