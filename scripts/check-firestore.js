const admin = require('firebase-admin');

// Initialize Firebase Admin with service account
const serviceAccount = require('../../serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`
});

const db = admin.firestore();

async function checkCollections() {
  try {
    console.log('Checking Firestore collections...');
    
    // List all collections
    const collections = await db.listCollections();
    console.log('Available collections:');
    collections.forEach(collection => {
      console.log(`- ${collection.id}`);
    });
    
    // Check galleryImages collection if it exists
    const galleryRef = db.collection('galleryImages');
    const snapshot = await galleryRef.get();
    
    if (snapshot.empty) {
      console.log('\nNo documents found in galleryImages collection');
    } else {
      console.log(`\nFound ${snapshot.size} documents in galleryImages:`);
      snapshot.forEach(doc => {
        console.log(`Document ID: ${doc.id}`, doc.data());
      });
    }
    
  } catch (error) {
    console.error('Error checking Firestore:', error);
  } finally {
    process.exit(0);
  }
}

checkCollections();
