# Video Setup Instructions

## 📹 **Adding Your Videos to the Hero Section**

### **Step 1: Create the Public Directory Structure**
```bash
mkdir -p "public/website videos"
```

### **Step 2: Add Your Videos**
Place your video files in the following location:
```
public/
└── website videos/
    ├── adukrom.mp4
    └── valley.mp4
```

### **Step 3: Video Requirements**
- **Format**: MP4 (recommended)
- **Codec**: H.264 for best browser compatibility
- **Size**: Optimized for web (recommended max 50MB per video)
- **Resolution**: 1920x1080 or higher for best quality
- **Duration**: Any length (they will loop automatically)

### **Current Video Sequence:**
1. **adukrom.mp4** - Plays first
2. **valley.mp4** - Plays second
3. **Loop** - Returns to adukrom.mp4

### **Features Already Implemented:**
- ✅ **Auto-play** with muted audio (browser-friendly)
- ✅ **Sequential playback** (adukrom → valley → repeat)
- ✅ **Manual controls** (click dots to switch videos)
- ✅ **Royal blue overlay** for brand consistency
- ✅ **Graceful fallback** to gradient background if videos missing
- ✅ **Mobile optimized** with playsInline
- ✅ **Loading states** with professional spinner

### **File Paths in Code:**
The component looks for videos at:
- `/website%20videos/adukrom.mp4`
- `/website%20videos/valley.mp4`

(The `%20` handles the space in "website videos")

### **Testing:**
Once you add the videos:
1. Restart the development server: `npm run dev`
2. Visit `http://localhost:3000`
3. Videos should auto-play with blue overlay
4. Use the dots in bottom-right to manually switch videos

### **Troubleshooting:**
- If videos don't play, check browser console for errors
- Ensure videos are in MP4 format with H.264 codec
- Check file permissions (videos should be readable)
- Try refreshing the page after adding videos

The hero section will work beautifully with or without videos! 🎬👑
