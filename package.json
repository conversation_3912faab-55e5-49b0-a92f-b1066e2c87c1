{"name": "royal-ghana-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@firebase/app": "^0.13.1", "@firebase/firestore": "^4.7.17", "@firebase/storage": "^0.13.13", "@heroicons/react": "^2.0.18", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "framer-motion": "^7.10.3", "next": "^15.3.3", "nodemailer": "^6.9.8", "react": "^18", "react-dom": "^18", "react-icons": "^5.5.0"}, "devDependencies": {"@types/eventsource": "^1.1.15", "@types/node": "^24.0.0", "@types/nodemailer": "^6.4.14", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5.8.3"}}