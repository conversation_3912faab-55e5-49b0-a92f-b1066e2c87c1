rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // <PERSON><PERSON><PERSON> deny all
    match /{document=**} {
      allow read: if false;
      allow write: if false;
    }
    
    // Public read access to specific collections
    match /galleryImages/{document} {
      allow read: if true;
      allow write: if request.auth != null && 
                   request.auth.token.admin == true;
    }
    
    match /partners/{document} {
      allow read: if true;
      allow create, update, delete: if request.auth != null && 
                                  request.auth.token.admin == true;
    }
    
    match /news/{document} {
      allow read: if true;
      allow create, update, delete: if request.auth != null && 
                                  request.auth.token.admin == true;
    }
  }
}
