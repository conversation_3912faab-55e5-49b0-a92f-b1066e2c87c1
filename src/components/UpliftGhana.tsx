'use client';

import React from 'react';

const UpliftGhana = () => {
  const initiatives = [
    {
      id: 1,
      title: 'Education for All',
      description: 'Providing quality education and resources to underprivileged children across Ghana.',
      icon: '🎓',
      stats: '5,000+ students supported'
    },
    {
      id: 2,
      title: 'Healthcare Access',
      description: 'Improving healthcare infrastructure and access to medical services in rural communities.',
      icon: '🏥',
      stats: '10+ clinics established'
    },
    {
      id: 3,
      title: 'Economic Empowerment',
      description: 'Supporting local entrepreneurs and small businesses through training and microfinance.',
      icon: '💼',
      stats: '500+ businesses supported'
    },
    {
      id: 4,
      title: 'Environmental Sustainability',
      description: 'Promoting sustainable practices and conservation efforts throughout Ghana.',
      icon: '🌱',
      stats: '50,000+ trees planted'
    }
  ];

  const successStories = [
    {
      id: 1,
      quote: "The Uplift Ghana initiative transformed our community. Our children now have access to quality education and our farmers have better tools to succeed.",
      author: "<PERSON> Ya<PERSON>, Community Leader",
      location: "Adukrom"
    },
    {
      id: 2,
      quote: "Thanks to the healthcare program, our village now has a functioning clinic. The difference it has made is immeasurable.",
      author: "Dr. <PERSON><PERSON><PERSON>",
      location: "Koforidua"
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-royalBlue to-royalNavy text-white">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">Uplift Ghana Initiative</h1>
          <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-xl text-gray-200 max-w-3xl mx-auto mb-8">
            Transforming communities and empowering lives across Ghana through sustainable development and cultural preservation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#donate"
              className="px-8 py-4 bg-royalGold text-royalBlue font-bold rounded-xl hover:bg-yellow-400 transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Donate Now
            </a>
            <a
              href="#initiatives"
              className="px-8 py-4 bg-transparent border-2 border-white text-white font-bold rounded-xl hover:bg-white/10 transition-colors duration-300"
            >
              Our Initiatives
            </a>
          </div>
        </div>
      </section>

      {/* Impact Stats */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {[
              { number: '10,000+', label: 'Lives Impacted' },
              { number: '50+', label: 'Communities Reached' },
              { number: '5', label: 'Key Initiatives' },
              { number: '100+', label: 'Local Partners' }
            ].map((stat, index) => (
              <div key={index} className="p-6 bg-white rounded-xl shadow-md">
                <div className="text-4xl font-bold text-royalBlue mb-2">{stat.number}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Initiatives */}
      <section id="initiatives" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-4">Our Initiatives</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              We're committed to creating sustainable change through targeted programs that address the most pressing needs in Ghanaian communities.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {initiatives.map((initiative) => (
              <div key={initiative.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="p-8 text-center">
                  <div className="text-5xl mb-4">{initiative.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{initiative.title}</h3>
                  <p className="text-gray-600 mb-4">{initiative.description}</p>
                  <div className="text-sm font-medium text-royalGold">{initiative.stats}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-royalBlue mb-4">Success Stories</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Hear from the communities and individuals whose lives have been transformed through our initiatives.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {successStories.map((story) => (
              <div key={story.id} className="bg-white p-8 rounded-xl shadow-lg">
                <div className="text-royalGold text-5xl mb-4">"</div>
                <p className="text-gray-700 text-lg italic mb-6">{story.quote}</p>
                <div className="font-bold text-gray-900">{story.author}</div>
                <div className="text-gray-500 text-sm">{story.location}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Donation CTA */}
      <section id="donate" className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-royalBlue to-royalNavy text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Join Us in Making a Difference</h2>
          <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
            Your support helps us continue our mission to uplift communities and preserve cultural heritage across Ghana.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/donate"
              className="px-8 py-4 bg-royalGold text-royalBlue font-bold rounded-xl hover:bg-yellow-400 transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Donate Now
            </a>
            <a
              href="/contact"
              className="px-8 py-4 bg-transparent border-2 border-white text-white font-bold rounded-xl hover:bg-white/10 transition-colors duration-300"
            >
              Contact Us
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default UpliftGhana;
