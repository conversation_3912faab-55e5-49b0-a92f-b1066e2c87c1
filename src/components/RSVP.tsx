'use client'

import { useState } from 'react'

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  country: string;
  events: string[];
  attendanceType: string;
  reminderPreference: string;
  notes: string;
}

const RSVP: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    country: '',
    events: [],
    attendanceType: '',
    reminderPreference: '',
    notes: ''
  })

  const countries = [
    'Ghana', 'Nigeria', 'South Africa', 'United States', 'United Kingdom', 'Canada', 'France', 'Germany', 'Other'
  ]

  const events = [
    'Royal Coronation Ceremony (Apr 15)',
    'Royal Gala Dinner (Apr 15)',
    'Global Economic Forum (Apr 16)'
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked
    
    if (type === 'checkbox') {
      if (name === 'events') {
        setFormData(prev => ({
          ...prev,
          events: checked 
            ? [...prev.events, value]
            : prev.events.filter(event => event !== value)
        }))
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    console.log('RSVP Form Data:', formData)
    // Handle form submission here
    alert('Thank you for your RSVP! We will contact you with further details.')
  }

  return (
    <section id="rsvp" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/8 via-transparent to-royalBlue/8"></div>
      
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-10 w-96 h-96 bg-gradient-to-br from-royalGold/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-80 h-80 bg-gradient-to-tl from-royalBlue/10 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            RSVP for Royal Events
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Join us for these historic celebrations. Please complete the form below to reserve your place at the royal events.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div
            className="bg-white/70 backdrop-blur-lg rounded-3xl p-8 border border-white/60 shadow-2xl"
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div
                >
                  <label className="block text-sm font-semibold text-royalBlue mb-2">
                    First Name *
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                    placeholder="Enter your first name"
                  />
                </div>

                <div
                >
                  <label className="block text-sm font-semibold text-royalBlue mb-2">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                    placeholder="Enter your last name"
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div
                >
                  <label className="block text-sm font-semibold text-royalBlue mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                    placeholder="Enter your email address"
                  />
                </div>

                <div
                >
                  <label className="block text-sm font-semibold text-royalBlue mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                    placeholder="Enter your phone number"
                  />
                </div>
              </div>

              {/* Country Selection */}
              <div
              >
                <label className="block text-sm font-semibold text-royalBlue mb-2">
                  Country *
                </label>
                <select
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                >
                  <option value="">Select your country</option>
                  {countries.map((country) => (
                    <option key={country} value={country}>{country}</option>
                  ))}
                </select>
              </div>

              {/* Events Selection */}
              <div
              >
                <label className="block text-sm font-semibold text-royalBlue mb-4">
                  Which events will you attend? *
                </label>
                <div className="space-y-3">
                  {events.map((event) => (
                    <label key={event} className="flex items-center p-3 bg-white/60 rounded-xl border border-gray-200 hover:bg-white/80 transition-all duration-300 cursor-pointer">
                      <input
                        type="checkbox"
                        name="events"
                        value={event}
                        checked={formData.events.includes(event)}
                        onChange={handleInputChange}
                        className="w-5 h-5 text-royalGold bg-white border-gray-300 rounded focus:ring-royalGold focus:ring-2"
                      />
                      <span className="ml-3 text-gray-700 font-medium">{event}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Attendance Type */}
              <div
              >
                <label className="block text-sm font-semibold text-royalBlue mb-4">
                  Attendance Type *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {['Physical Attendance', 'Online Attendance'].map((type) => (
                    <label key={type} className="flex items-center p-4 bg-white/60 rounded-xl border border-gray-200 hover:bg-white/80 transition-all duration-300 cursor-pointer">
                      <input
                        type="radio"
                        name="attendanceType"
                        value={type}
                        checked={formData.attendanceType === type}
                        onChange={handleInputChange}
                        className="w-5 h-5 text-royalGold bg-white border-gray-300 focus:ring-royalGold focus:ring-2"
                      />
                      <span className="ml-3 text-gray-700 font-medium">{type}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Reminder Preference */}
              <div
              >
                <label className="block text-sm font-semibold text-royalBlue mb-4">
                  Reminder Preference
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {['Email Reminder', 'SMS Reminder'].map((preference) => (
                    <label key={preference} className="flex items-center p-4 bg-white/60 rounded-xl border border-gray-200 hover:bg-white/80 transition-all duration-300 cursor-pointer">
                      <input
                        type="radio"
                        name="reminderPreference"
                        value={preference}
                        checked={formData.reminderPreference === preference}
                        onChange={handleInputChange}
                        className="w-5 h-5 text-royalGold bg-white border-gray-300 focus:ring-royalGold focus:ring-2"
                      />
                      <span className="ml-3 text-gray-700 font-medium">{preference}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Additional Notes */}
              <div
              >
                <label className="block text-sm font-semibold text-royalBlue mb-2">
                  Additional Notes
                </label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300 resize-none"
                  placeholder="Any special requirements or additional information..."
                />
              </div>

              {/* Submit Button */}
              <div
                className="text-center pt-6"
              >
                <button
                  type="submit"
                  className="px-12 py-4 relative overflow-hidden text-royalBlue font-bold text-lg rounded-xl shadow-2xl border-2 border-yellow-300 group"
                  style={{
                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                    boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5), inset 0 3px 6px rgba(255, 255, 255, 0.4), inset 0 -3px 6px rgba(0, 0, 0, 0.3)'
                  }}
                >
                  {/* Shining animation overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
                  <span className="relative z-10 font-extrabold">
                    <i className="fas fa-paper-plane mr-2"></i>
                    Submit RSVP
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RSVP
