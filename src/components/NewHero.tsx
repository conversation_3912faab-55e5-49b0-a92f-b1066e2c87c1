'use client'

import { useState, useEffect, useRef } from 'react'

const NewHero: React.FC = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  const [videoBlocked, setVideoBlocked] = useState(false)
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)
  
  const videos = [
    '/website-videos/video1.mp4',
    '/website-videos/video2.mp4'
  ]

  // Countdown timer effect
  useEffect(() => {
    const targetDate = new Date('2025-08-29T03:30:00-05:00').getTime()

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000)
        })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Video carousel effect
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleVideoEnd = () => {
      setCurrentVideoIndex((prevIndex) => (prevIndex + 1) % videos.length)
    }

    const handleVideoLoad = (): void => {
      video.muted = true
      video.playsInline = true
      video.addEventListener('ended', handleVideoEnd)

      video.play().catch((error) => {
        console.warn('Video autoplay blocked:', error)
        setVideoBlocked(true)
      })
    }

    // Set up the video when it's ready
    if (video.readyState >= 3) {
      handleVideoLoad()
    } else {
      video.addEventListener('loadeddata', handleVideoLoad, { once: true })
    }

    return () => {
      video.removeEventListener('loadeddata', handleVideoLoad)
      video.removeEventListener('ended', handleVideoEnd)
    }
  }, [currentVideoIndex])

  // Handle user interaction to enable video playback
  const handleUserInteraction = (): void => {
    const video = videoRef.current
    if (video && videoBlocked) {
      video.muted = true
      video.playsInline = true

      video.play().then(() => {
        setVideoBlocked(false)
      }).catch((error) => {
        console.warn('Manual video play failed:', error)
      })
    }
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden -mt-20">
      {/* Video Background */}
      <video
        key={currentVideoIndex}
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover transition-opacity duration-1000"
        autoPlay
        muted
        playsInline
        controls={false}
        preload="auto"
        style={{ objectFit: 'cover' }}
      >
        <source src={videos[currentVideoIndex]} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Video blocked overlay - click to enable */}
      {videoBlocked && (
        <div
          className="absolute inset-0 bg-royalBlue/90 flex items-center justify-center cursor-pointer z-20"
          onClick={handleUserInteraction}
        >
          <div className="text-center text-white">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 hover:bg-white/30 transition-colors">
              <i className="fas fa-play text-3xl ml-1"></i>
            </div>
            <p className="text-lg font-semibold">Click to enable video background</p>
          </div>
        </div>
      )}

      {/* Blue Overlay */}
      <div className="absolute inset-0 bg-royalBlue/70"></div>

      {/* Gradient Overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue/50 via-royalBlue/40 to-royalBlue/60"></div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('/pattern.png')] bg-repeat opacity-20"></div>
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-royalGold/20 to-transparent rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-tl from-royalGold/15 to-transparent rounded-full blur-xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10 pt-24">
        <div className="text-center">
          <div className="animate-fade-in-up">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6 leading-tight drop-shadow-lg">
              <span className="block animate-slide-in-left animation-delay-200">Adukrom Kingdom</span>
              <span className="block text-royalGold animate-slide-in-right animation-delay-400">The Crown of Africa</span>
              <span className="block text-royalGold md:text-3xl lg:text-4xl mt-4 font-light animate-fade-in animation-delay-600">
                Rise of a New Era
              </span>
            </h1>
          </div>

          <p className="text-xl md:text-2xl text-white mb-12 max-w-4xl mx-auto leading-relaxed drop-shadow-lg animate-fade-in-up animation-delay-800">
            Join us in celebrating a new era of prosperity, heritage, and visionary leadership for our people and the global community.
          </p>

          {/* Countdown Timer */}
          <div className="mb-12 animate-fade-in-up animation-delay-1000">
            <h3 className="text-2xl md:text-3xl font-bold mb-6 drop-shadow-lg relative">
              <span
                className="bg-gradient-to-r from-royalGold via-yellow-300 to-royalGold bg-clip-text text-transparent animate-pulse"
                style={{
                  textShadow: '0 0 8px rgba(255, 215, 0, 0.4), 0 0 16px rgba(255, 215, 0, 0.2)',
                  filter: 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.5))',
                  animation: 'glow-pulse-subtle 3s ease-in-out infinite alternate'
                }}
              >
                Royal Coronation Countdown
              </span>
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { label: 'Days', value: timeLeft.days },
                { label: 'Hours', value: timeLeft.hours },
                { label: 'Minutes', value: timeLeft.minutes },
                { label: 'Seconds', value: timeLeft.seconds }
              ].map((item, index) => (
                <div
                  key={item.label}
                  className={`bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:bg-white/20 hover:scale-105 transition-all duration-300 animate-scale-in animation-delay-${1200 + index * 100}`}
                >
                  <div className="text-3xl md:text-4xl font-bold text-royalGold mb-2 animate-number-flip">
                    {item.value.toString().padStart(2, '0')}
                  </div>
                  <div className="text-white/80 text-sm uppercase tracking-wider">
                    {item.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up animation-delay-1000">
            <a
              href="/tickets"
              className="relative overflow-hidden text-royalBlue px-8 py-4 rounded-full font-bold text-lg shadow-2xl border-2 border-yellow-300 group hover:scale-110 hover:-translate-y-1 transition-all duration-300 min-w-[200px] text-center"
              style={{
                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
              <span className="relative z-10 font-extrabold text-shadow">Get Your Tickets</span>
            </a>
            <a
              href="#about-allen"
              className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-royalBlue hover:scale-110 hover:-translate-y-1 transition-all duration-300 min-w-[200px]"
            >
              Learn More
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default NewHero
