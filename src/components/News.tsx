'use client';

import React from 'react';

const News = () => {
  const newsItems = [
    {
      id: 1,
      title: 'Royal Coronation Announcement',
      date: 'June 10, 2025',
      excerpt: 'His Royal Majesty will be officially crowned in a grand ceremony on June 25th, 2025.',
      image: '/Website Images/Royal Coronation Ceremony.png',
      category: 'Announcement'
    },
    {
      id: 2,
      title: 'Community Development Initiative',
      date: 'June 5, 2025',
      excerpt: 'New community development programs announced to uplift the Adukrom region.',
      image: '/Website Images/ghana-osuodumgya-event-2023.png',
      category: 'Community'
    },
    {
      id: 3,
      title: 'Cultural Heritage Celebration',
      date: 'May 28, 2025',
      excerpt: 'Annual cultural festival to showcase the rich heritage of Adukrom.',
      image: '/Website Images/ghana-flag-waving.png',
      category: 'Culture'
    },
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-royalBlue mb-4">Latest News</h2>
          <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Stay updated with the latest news and announcements from the Royal Palace.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {newsItems.map((item) => (
            <div key={item.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <div className="h-48 overflow-hidden">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                />
              </div>
              <div className="p-6">
                <span className="inline-block px-3 py-1 text-xs font-semibold text-royalBlue bg-royalBlue/10 rounded-full mb-2">
                  {item.category}
                </span>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-gray-600 text-sm mb-4">{item.excerpt}</p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">{item.date}</span>
                  <button className="text-royalGold hover:text-royalBlue font-medium text-sm">
                    Read More →
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <button className="px-8 py-3 bg-royalBlue text-white font-medium rounded-lg hover:bg-royalBlue/90 transition-colors duration-300">
            View All News
          </button>
        </div>
      </div>
    </section>
  );
};

export default News;
