'use client'

import { useState, useRef, useEffect } from 'react';

// Gallery images data
const galleryImages = [
  {
    src: "/Website Images/agricultural-practices-ghana.jpg",
    alt: "Agricultural Practices in Ghana"
  },
  {
    src: "/Website Images/the-ellison-foundation-leader.jpg",
    alt: "The Ellison Foundation Leader"
  },
  {
    src: "/Website Images/Allenlook.jpg",
    alt: "His Royal Majesty King <PERSON>"
  }
];

const AboutAllen: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setSelectedImage(null);
      }
    };

    if (selectedImage) {
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [selectedImage]);
  return (
    <section id="about-allen" className="py-16 relative overflow-hidden bg-gradient-to-br from-cream via-ivory to-cream">
      {/* Decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute inset-0 bg-[url('/Website%20Images/pattern-bg.png')] opacity-5"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-royalBlue mb-6">
            About His Royal Majesty
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8 rounded-full"></div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
            His Royal Majesty King Allen Ellison, Mpuntuhene of Adukrom - A visionary leader dedicated to preserving Ghana's rich heritage while building a prosperous future for all.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Biography Card */}
            <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-8 border border-white/40 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
              <h3 className="text-2xl font-bold text-royalBlue mb-6 border-b border-royalGold/30 pb-4">Royal Biography</h3>

              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed text-base">
                  Born into the noble lineage of the Ashanti Dynasty, one of Ghana's most revered royal houses, His Royal Majesty was groomed from an early age in the traditions and customs of royal leadership, receiving both traditional education and modern academic training.
                </p>
                <p className="text-gray-700 leading-relaxed text-base">
                  After completing his education at prestigious institutions both in Ghana and abroad, His Majesty dedicated himself to public service and cultural preservation. His work in sustainable development and cultural heritage has earned him numerous accolades and the respect of leaders worldwide.
                </p>
                <a
                  href="/biography"
                  className="inline-block mt-6 text-royalBlue hover:text-royalGold transition-colors duration-300 font-medium border-b border-transparent hover:border-royalGold/50 pb-1"
                >
                  Read Full Biography →
                </a>
              </div>
            </div>

            {/* Combined Vision & Mission Card */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-royalBlue/5 to-royalBlue/20 backdrop-blur-lg rounded-2xl p-6 border border-royalBlue/20 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <h3 className="text-xl font-bold text-royalBlue mb-4 pb-2 border-b border-royalBlue/20">Vision</h3>
                <p className="text-gray-700 leading-relaxed text-sm">
                  To establish Ghana as a beacon of cultural preservation and sustainable development in Africa, where traditional wisdom and modern innovation harmoniously coexist to create prosperity for all citizens.
                </p>
              </div>

              <div className="bg-gradient-to-br from-royalGold/5 to-royalGold/20 backdrop-blur-lg rounded-2xl p-6 border border-royalGold/20 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <h3 className="text-xl font-bold text-royalBlue mb-4 pb-2 border-b border-royalGold/20">Mission</h3>
                <p className="text-gray-700 leading-relaxed text-sm">
                  To lead with wisdom, serve with humility, and govern with justice, ensuring that every citizen has the opportunity to reach their full potential through sustainable development and cultural preservation.
                </p>
              </div>
            </div>

            {/* Royal Message Card */}
            <div className="relative bg-gradient-to-br from-royalBlue/5 to-royalGold/5 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-xl overflow-hidden">
              {/* Decorative corner elements */}
              <div className="absolute top-0 right-0 w-24 h-24 bg-royalGold/5 rounded-bl-full transform translate-x-12 -translate-y-12"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-royalBlue/5 rounded-tr-full transform -translate-x-12 translate-y-12"></div>
              
              <div className="relative z-10">
                <h3 className="text-2xl font-bold text-royalBlue mb-6 border-b border-royalGold/30 pb-4">Royal Message</h3>

                <blockquote className="relative pl-6 border-l-4 border-royalGold/50 mb-6">
                  <p className="text-gray-700 italic leading-relaxed text-base mb-6">
                    "Our kingdom's strength lies in honoring our ancestors while building bridges to the future. Together, we will create prosperity that respects our traditions and embraces innovation for The Rebirth of Adukrom."
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-[1px] bg-royalGold mr-4"></div>
                    <div className="text-right">
                      <div className="text-royalGold font-semibold text-sm uppercase tracking-wider">His Royal Majesty</div>
                      <div className="text-royalBlue font-medium">King Allen Ellison</div>
                    </div>
                  </div>
                </blockquote>
              </div>
            </div>
          </div>

          {/* Profile Image - Right Side (1/3 width) */}
          <div className="relative lg:sticky lg:top-8">
            <div className="relative group">
              {/* Main image container with border and shadow */}
              <div className="relative overflow-hidden rounded-2xl border-4 border-white shadow-2xl transition-all duration-300">
                <img
                  src="/Website Images/Allen-Ellison-Profile-scaled-1.png"
                  alt="His Royal Majesty King Allen Ellison"
                  className="w-full h-auto object-cover aspect-[3/4] transition-transform duration-500 group-hover:scale-102"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                    if (nextElement) {
                      nextElement.style.display = 'flex';
                    }
                  }}
                />
                {/* Fallback placeholder */}
                <div className="absolute inset-0 bg-gradient-to-br from-royalBlue/10 to-royalGold/10 flex items-center justify-center" style={{display: 'none'}}>
                  <div className="text-6xl text-royalBlue/20">
                    {/* Fallback content removed */}
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </div>

            {/* Title and position */}
            <div className="mt-6 text-center">
              <div className="bg-white/90 backdrop-blur-sm px-6 py-4 rounded-xl shadow-lg border border-white/40">
                <h4 className="text-xl font-bold text-royalBlue">His Royal Majesty</h4>
                <p className="text-royalGold font-semibold text-xl mt-1">King Allen Ellison</p>
                <p className="text-royalBlue/80 text-sm mt-1">Mpuntuhene of Adukrom</p>
                <div className="mt-3 pt-3 border-t border-royalGold/20">
                  <p className="text-sm text-royalBlue/80">Leader • Visionary • Custodian of Tradition</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutAllen
