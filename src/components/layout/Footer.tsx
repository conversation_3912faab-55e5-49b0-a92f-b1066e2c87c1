'use client'

import Link from 'next/link'

const Footer: React.FC = () => {
  const quickLinks = [
    { name: 'Home', href: '/adukrom' },
    { name: 'Events', href: '/events' },
    { name: 'News', href: '/news' },
    { name: 'Partners', href: '/partners' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'Streaming', href: '/streaming' },
    { name: 'Tickets', href: '/tickets' }
  ]

  const socialLinks = [
    { icon: 'fab fa-facebook-f', href: '#', color: 'hover:text-blue-500' },
    { icon: 'fab fa-twitter', href: '#', color: 'hover:text-blue-400' },
    { icon: 'fab fa-instagram', href: '#', color: 'hover:text-pink-500' },
    { icon: 'fab fa-youtube', href: '#', color: 'hover:text-red-500' }
  ]

  return (
    <footer className="relative bg-royalBlue text-white overflow-hidden">
      {/* Golden gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/15 via-royalGold/5 to-royalGold/10"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>
      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div 
            className="md:col-span-2"
          >
            <div className="flex items-center mb-4">
              <div className="mr-3 text-royalGold text-3xl">👑</div>
              <div>
                <h3 className="text-xl font-serif font-bold">Adukrom Kingdom</h3>
                <p className="text-royalGold text-sm">The Crown of Africa: Rise of a New Era</p>
              </div>
            </div>
            <p className="text-white/80 leading-relaxed mb-6 max-w-md">
              Adukrom Kingdom is a traditional royal kingdom located in Ghana’s Eastern Region. Governed by traditional council and royal leadership, the Kingdom is focused on empowering its people through cultural preservation, economic innovation, and international diplomacy.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className={`w-10 h-10 rounded-full bg-royalGold/20 flex items-center justify-center text-royalGold transition-colors duration-300 ${social.color}`}
                >
                  <i className={social.icon}></i>
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div 
          >
            <h4 className="text-lg font-bold mb-4 text-royalGold">Quick Links</h4>
            <ul className="space-y-2">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link 
                    href={link.href}
                    className="text-white/80 hover:text-royalGold transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div 
          >
            <h4 className="text-lg font-bold mb-4 text-royalGold">Contact Us</h4>
            <div className="space-y-3 text-sm">
              <div className="flex items-start">
                <i className="fas fa-map-marker-alt text-royalGold mr-3 mt-1"></i>
                <div>
                  <p className="text-white/80">P.O. Box 1 Adukron-Akuapem</p>
                  <p className="text-white/80">Ghana, West Africa</p>
                </div>
              </div>
              <div className="flex items-center">
                <i className="fas fa-phone text-royalGold mr-3"></i>
                <p className="text-white/80">+****************</p>
              </div>
              <div className="flex items-center">
                <i className="fas fa-envelope text-royalGold mr-3"></i>
                <p className="text-white/80"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div 
          className="border-t border-white/10 mt-8 pt-8 text-center"
        >
          <p className="text-white/60 text-sm">
            © 2025 Adukrom Kingdom. All rights reserved.
          </p>
          <div className="flex justify-center space-x-6 mt-4">
            <a href="/privacy-policy" className="text-white/60 hover:text-royalGold transition-colors duration-300 text-sm">Privacy Policy</a>
            <a href="/terms-of-service" className="text-white/60 hover:text-royalGold transition-colors duration-300 text-sm">Terms of Service</a>
            <a href="/cookie-policy" className="text-white/60 hover:text-royalGold transition-colors duration-300 text-sm">Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
