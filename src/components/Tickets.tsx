'use client';

import React from 'react';

const Tickets = () => {
  const ticketOptions = [
    {
      id: 1,
      name: 'Standard Admission',
      price: 50,
      features: [
        'General seating',
        'Event program',
        'Commemorative gift'
      ],
      popular: false
    },
    {
      id: 2,
      name: 'VIP Experience',
      price: 150,
      features: [
        'Premium seating',
        'Exclusive lounge access',
        'Meet & greet opportunity',
        'Commemorative gift',
        'VIP parking'
      ],
      popular: true
    },
    {
      id: 3,
      name: 'Royal Package',
      price: 300,
      features: [
        'Front row seating',
        'Exclusive backstage tour',
        'Photo with His Royal Majesty',
        'Gourmet dining experience',
        'VIP parking',
        'Limited edition souvenir'
      ],
      popular: false
    }
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-royalBlue mb-4">Get Your Tickets</h2>
          <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Secure your spot at the most prestigious royal events of the year.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {ticketOptions.map((ticket) => (
            <div 
              key={ticket.id}
              className={`relative rounded-xl overflow-hidden shadow-lg ${
                ticket.popular ? 'ring-2 ring-royalGold transform -translate-y-2' : 'bg-white'
              }`}
            >
              {ticket.popular && (
                <div className="absolute top-0 right-0 bg-royalGold text-white text-xs font-bold px-4 py-1 rounded-bl-lg">
                  MOST POPULAR
                </div>
              )}
              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{ticket.name}</h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-royalBlue">${ticket.price}</span>
                  <span className="text-gray-500">/person</span>
                </div>
                <ul className="space-y-3 mb-8">
                  {ticket.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <svg className="h-5 w-5 text-royalGold mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button className="w-full bg-royalBlue text-white font-medium py-3 px-6 rounded-lg hover:bg-royalBlue/90 transition-colors duration-300">
                  Purchase Now
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 max-w-4xl mx-auto">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Group Bookings</h3>
          <p className="text-gray-600 mb-6">
            Planning to attend with a group? We offer special rates for groups of 10 or more. 
            Contact our events team for more information and to make arrangements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Your email address"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent"
            />
            <button className="bg-royalGold text-white font-medium py-3 px-6 rounded-lg hover:bg-royalGold/90 transition-colors duration-300">
              Contact Us
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Tickets;
