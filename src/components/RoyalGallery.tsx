'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { getGalleryImages } from '@/lib/firebase';
import { GalleryImage } from '@/lib/firebase';

interface CarouselImage {
  url: string;
  title: string;
  description: string;
  category: string;
}
import { FaTimes, FaChevronLeft, FaChevronRight, FaImage } from 'react-icons/fa';

const RoyalGallery: React.FC<{ isHomepage?: boolean }> = ({ isHomepage = false }) => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [retryCount, setRetryCount] = useState(0);
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    let isMounted = true;

    const fetchGalleryImages = async () => {
      // Only run on client side
      if (typeof window === 'undefined') return;

      try {
        if (isMounted) setLoading(true);
        const galleryImages = await getGalleryImages();

        if (isMounted) {
          setImages(galleryImages);
          setError(null);
        }
      } catch (err) {
        console.error('Error in fetchGalleryImages:', err);
        if (isMounted) {
          setError('Failed to load gallery images. ' + (err instanceof Error ? err.message : 'Please try again later.'));
        }
      } finally {
        if (isMounted) setLoading(false);
      }
    };

    fetchGalleryImages();
    
    return () => {
      isMounted = false;
    };
  }, [retryCount]);

  const openModal = (image: GalleryImage, index: number) => {
    setSelectedImage(image);
    setCurrentIndex(index);
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setSelectedImage(null);
    document.body.style.overflow = 'unset';
  };

  const navigate = (direction: 'prev' | 'next') => {
    if (!selectedImage || images.length <= 1) return;
    
    let newIndex;
    if (direction === 'next') {
      newIndex = (currentIndex + 1) % images.length;
    } else {
      newIndex = (currentIndex - 1 + images.length) % images.length;
    }
    
    setSelectedImage(images[newIndex]);
    setCurrentIndex(newIndex);
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  // Partner logos with optimized image loading
  const partnerLogos = [
    { 
      id: 'remit-global', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Fremit-global.png?alt=media',
      name: 'Remit Global',
      width: 180,
      height: 80
    },
    { 
      id: 'royal-lion', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Froyal-lion.png?alt=media',
      name: 'Royal Lion',
      width: 180,
      height: 80
    },
    { 
      id: 'tef', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Ftef.png?alt=media',
      name: 'TEF',
      width: 180,
      height: 80
    },
    { 
      id: 'lightace', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Flightace.png?alt=media',
      name: 'LightAce Global',
      width: 180,
      height: 80
    },
    { 
      id: 'akuapem', 
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Fakuapem.png?alt=media',
      name: 'Akuapem Nifaman Council',
      width: 180,
      height: 80
    },
  ];

  // Carousel images with optimized loading and placeholders
  const carouselImages: CarouselImage[] = [
    {
      url: '/images/gallery/royal-event-1.jpg',
      title: 'Royal Gathering',
      description: 'His Majesty with distinguished guests at the annual royal gathering',
      category: 'events'
    },
    {
      url: '/images/gallery/cultural-festival.jpg',
      title: 'Cultural Festival',
      description: 'Vibrant display of traditional dances and cultural heritage',
      category: 'culture'
    },
    {
      url: '/images/gallery/community-outreach.jpg',
      title: 'Community Outreach',
      description: 'Royal family engaging with local community members',
      category: 'community'
    },
    {
      url: '/images/gallery/royal-palace.jpg',
      title: 'Royal Palace',
      description: 'The magnificent royal palace in all its glory',
      category: 'architecture'
    },
    {
      url: '/images/gallery/traditional-ceremony.jpg',
      title: 'Traditional Ceremony',
      description: 'Ancient rites and traditions being performed',
      category: 'ceremony'
    },
    {
      url: '/images/gallery/royal-garden.jpg',
      title: 'Royal Gardens',
      description: 'The beautifully maintained royal gardens',
      category: 'nature'
    }
  ];
  
  // Auto-advance carousel
  useEffect(() => {
    if (carouselImages.length <= 1) return;
    
    const timer = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % Math.ceil(carouselImages.length / 3));
    }, 5000);
    
    return () => clearInterval(timer);
  }, [carouselImages.length]);
  
  // Use carousel images for the carousel
  const displayImages = carouselImages;
  
  // Function to handle opening modal with proper typing
  const handleOpenModal = (image: CarouselImage | GalleryImage, index: number) => {
    if ('id' in image) {
      // It's a GalleryImage
      openModal(image, index);
    } else {
      // It's a CarouselImage - create a compatible object
      const galleryImage: GalleryImage = {
        id: `carousel-${index}`,
        url: image.url,
        title: image.title,
        description: image.description,
        category: image.category,
        uploadedAt: new Date()
      };
      openModal(galleryImage, index);
    }
  };
  
  // Function to handle image loading errors
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.style.display = 'none';
    const fallback = document.createElement('div');
    fallback.className = 'w-full h-full flex items-center justify-center bg-gray-100';
    fallback.textContent = target.alt || 'Image not available';
    target.parentNode?.insertBefore(fallback, target);
  };
  
  // Function to get image ID for key prop
  const getImageId = (image: CarouselImage | GalleryImage, index: number): string => {
    return 'id' in image ? image.id : `carousel-${index}`;
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % displayImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + displayImages.length) % displayImages.length);
  };

  if (loading) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-8">Royal Gallery</h2>
          <div className="flex justify-center">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex-shrink-0 w-full sm:w-1/2 md:w-1/3 px-2">
                <div className="bg-gray-200 rounded-lg animate-pulse aspect-square flex items-center justify-center">
                  <FaImage className="text-gray-400 text-4xl" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Royal Gallery</h2>
          <div className="max-w-md mx-auto bg-red-50 border border-red-200 rounded-lg p-6">
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={handleRetry}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className="py-12">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Royal Gallery</h2>
          <div className="max-w-md mx-auto bg-gray-50 border border-gray-200 rounded-lg p-6">
            <FaImage className="text-gray-400 text-5xl mx-auto mb-4" />
            <p className="text-gray-600 mb-4">No images found in the gallery.</p>
            <button 
              onClick={handleRetry}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <section id="royal-gallery" className={`py-12 md:py-16 relative overflow-hidden ${isHomepage ? 'bg-white' : 'bg-gray-50'}`}>
      <div className="container mx-auto px-4 relative z-10">
        {!isHomepage && (
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Royal Gallery
            </h2>
            <div className="w-20 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Explore the rich cultural heritage and royal traditions through our curated gallery.
            </p>
          </div>
        )}

        <div className="mb-16 relative">
          {/* Carousel Container */}
          <div className="relative overflow-hidden">
            {/* Navigation Arrows */}
            <button 
              onClick={prevSlide}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-all ml-2"
              aria-label="Previous image"
            >
              <FaChevronLeft className="w-6 h-6" />
            </button>
            
            {/* Carousel Track */}
            <div className="flex transition-transform duration-300 ease-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {displayImages.map((item, index) => {
                // Handle both CarouselImage and GalleryImage types
                const image = item as CarouselImage;
                const title = image.title || `Gallery image ${index + 1}`;
                const description = 'description' in image ? image.description : '';
                
                return (
                  <div
                    key={`carousel-${index}-${title}`}
                    className="flex-shrink-0 w-full sm:w-1/2 md:w-1/3 px-2"
                  >
                    <div 
                      className="relative overflow-hidden rounded-lg cursor-pointer group aspect-square"
                      onClick={() => handleOpenModal(image, index)}
                    >
                      <Image
                        src={image.url}
                        alt={title}
                        width={400}
                        height={300}
                        className="w-full h-full object-cover transform transition-transform duration-300 group-hover:scale-110"
                        priority={index < 3}
                      />
                      {title && (
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                          <h3 className="text-white font-medium text-sm md:text-base">
                            {typeof title === 'string' && title.includes(':') 
                              ? title.split(':').pop()?.trim() 
                              : title}
                          </h3>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            <button 
              onClick={nextSlide}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-all mr-2"
              aria-label="Next image"
            >
              <FaChevronRight className="w-6 h-6" />
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-6 space-x-2">
            {displayImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  currentSlide === index ? 'bg-royalGold' : 'bg-gray-300'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Image Modal */}
        {selectedImage && (
          <div 
            className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div 
              className="relative w-full max-w-5xl max-h-[90vh]"
              onClick={e => e.stopPropagation()}
            >
              <button 
                className="absolute -top-10 right-0 text-white hover:text-royalGold transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedImage(null);
                }}
              >
                <FaTimes className="text-2xl" />
              </button>
              <div className="bg-white rounded-lg overflow-hidden shadow-2xl">
                <div className="relative h-[70vh] bg-black">
                  <img 
                    src={selectedImage.url} 
                    alt={selectedImage.title || 'Gallery image'}
                    className="w-full h-full object-contain"
                  />
                  {images.length > 1 && (
                    <>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('prev');
                        }}
                        className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors"
                      >
                        <FaChevronLeft />
                      </button>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('next');
                        }}
                        className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors"
                      >
                        <FaChevronRight />
                      </button>
                    </>
                  )}
                </div>
                <div className="p-4 bg-white">
                  {selectedImage.title && (
                    <h3 className="text-lg font-semibold text-gray-900">
                      {selectedImage.title.includes(':') 
                        ? selectedImage.title.split(':').pop()?.trim() 
                        : selectedImage.title}
                    </h3>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {isHomepage && images.length > 0 && (
          <div className="text-center mt-16">
            <div className="bg-gradient-to-r from-royalBlue/5 to-royalGold/5 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-xl max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-royalBlue mb-4">Explore Our Gallery</h3>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Discover the complete collection of royal images, historical documents, and cultural artifacts in our comprehensive digital archive.
              </p>
              <a
                href="/gallery"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 transform"
              >
                <i className="fas fa-images mr-2"></i>
                View Full Gallery
              </a>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default RoyalGallery;
