'use client'

import { useEffect, useState } from 'react'
import { setupScrollAnimations, addFloatingAnimation, addShimmerEffect } from '../utils/scrollAnimations'

interface ScrollAnimationProviderProps {
  children: React.ReactNode
}

const ScrollAnimationProvider: React.FC<ScrollAnimationProviderProps> = ({ children }) => {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)

    // Only run animations after component is mounted (client-side)
    if (typeof window !== 'undefined') {
      // Small delay to ensure DOM is ready
      const timer = setTimeout(() => {
        try {
          // Initialize scroll animations
          setupScrollAnimations()

          // Add floating animation to decorative elements
          addFloatingAnimation('.animate-float')

          // Add shimmer effect to buttons
          addShimmerEffect('.bg-gradient-to-r')
        } catch (error) {
          console.warn('Animation setup failed:', error)
        }
      }, 100)

      return () => {
        clearTimeout(timer)
      }
    }
  }, [])

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return <>{children}</>
  }

  return <>{children}</>
}

export default ScrollAnimationProvider
