'use client';

import { useState, useEffect } from 'react';
import { getPartners, getStorageUrl } from '@/lib/firebase';
import Link from 'next/link';

// Base partner type with required fields
interface BasePartner {
  id: string;
  name: string;
  description: string;
  category: string;
  website?: string;
  logo?: string;
}

interface PartnerWithLogo extends BasePartner {
  logoUrl?: string;
  logo?: string;
}

interface FallbackPartner extends BasePartner {
  logo: string;
  website: string;
  logoUrl?: never; // This ensures FallbackPartner can't have logoUrl
}

const StrategicPartners: React.FC = () => {
  const [partners, setPartners] = useState<PartnerWithLogo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPartnersWithLogos = async () => {
      // Only run on client side
      if (typeof window === 'undefined') return;

      try {
        setLoading(true);
        const partnersData = await getPartners();
        
        // Filter to only include the 5 specific partners we want to display
        const filteredPartners = partnersData.filter(partner => 
          ['remit-global', 'royal-lion', 'tef', 'lightace', 'akuapem'].includes(partner.id)
        );
        
        // Fetch logo URLs for each partner
        const partnersWithLogos = await Promise.all(
          filteredPartners.map(async (partner) => {
            try {
              if (partner.logo) {
                const logoUrl = await getStorageUrl(partner.logo, 'partner');
                return { ...partner, logoUrl };
              }
              return partner;
            } catch (error) {
              console.error(`Error fetching logo for ${partner.name}:`, error);
              return partner; // Return partner without logo URL if there's an error
            }
          })
        );
        
        setPartners(partnersWithLogos);
      } catch (error) {
        console.error('Error fetching partners:', error);
        setError('Failed to load partners. Using fallback data.');
      } finally {
        setLoading(false);
      }
    };

    fetchPartnersWithLogos();
  }, []);

  // Fallback partners data with direct image URLs
  const fallbackPartners: FallbackPartner[] = [
    {
      id: 'remit-global',
      name: 'Remit Global',
      description: 'Strategic financial partner supporting the Kingdom\'s economic initiatives.',
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Fremit-global.png?alt=media',
      website: 'https://remitglobalinc.com/',
      category: 'financial'
    },
    {
      id: 'royal-lion',
      name: 'Royal Lion',
      description: 'Heritage and cultural partner preserving the Kingdom\'s traditions.',
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Froyal-lion.png?alt=media',
      website: 'https://queenmotherlatonja.com/',
      category: 'cultural'
    },
    {
      id: 'tef',
      name: 'TEF',
      description: 'Educational development partner advancing knowledge and skills in the community.',
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Ftef.png?alt=media',
      website: 'https://ellisonoutreachfoundation.com/',
      category: 'education'
    },
    {
      id: 'lightace',
      name: 'LightAce Global',
      description: 'Innovation and technology partner bringing digital solutions to the Kingdom.',
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Flightace.png?alt=media',
      website: 'https://lightaceglobal.wixsite.com/lightaceglobal',
      category: 'technology'
    },
    {
      id: 'akuapem',
      name: 'Akuapem Nifaman Council',
      description: 'Traditional governance partner supporting the Kingdom\'s leadership structure.',
      logo: 'https://firebasestorage.googleapis.com/v0/b/your-project-id.appspot.com/o/partners%2Fakuapem.png?alt=media',
      website: 'https://www.akuapemnifamancouncil.com',
      category: 'governance'
    }
  ];

  // Define the list of partner IDs we want to display
  const targetPartnerIds = ['remit-global', 'royal-lion', 'tef', 'lightace', 'akuapem'];
  
  // Create a map of fallback partners by ID for quick lookup
  const fallbackPartnersMap = fallbackPartners.reduce<Record<string, FallbackPartner>>((acc, partner) => {
    acc[partner.id] = partner;
    return acc;
  }, {});
  
  // Filter and merge partners with fallback data if needed
  const finalPartners = targetPartnerIds.map(id => {
    const fetchedPartner = partners.find(p => p.id === id);
    if (fetchedPartner) {
      return fetchedPartner;
    }
    return fallbackPartnersMap[id] || null;
  }).filter((partner): partner is PartnerWithLogo | FallbackPartner => partner !== null);

  // Helper function to check if a partner has a logo URL
  const hasLogoUrl = (partner: PartnerWithLogo | FallbackPartner): partner is PartnerWithLogo => {
    return 'logoUrl' in partner && partner.logoUrl !== undefined;
  };
  
  // Helper function to get the logo class
  const getLogoClass = (partner: PartnerWithLogo | FallbackPartner): string => {
    return 'logo' in partner && partner.logo ? partner.logo : 'fas fa-building';
  };
  
  // Ensure all partners have required fields with defaults
  const getSafePartnerData = (partner: PartnerWithLogo | FallbackPartner): BasePartner => {
    return {
      id: partner.id || 'unknown',
      name: partner.name || 'Partner',
      description: partner.description || 'Strategic partner',
      website: partner.website || '#',
      category: partner.category || 'general',
      ...('logo' in partner && { logo: partner.logo })
    };
  };



  return (
    <section id="strategic-partners" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Strategic Partners of the Crown
          </h2>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            At the heart of the Kingdom's vision for prosperity, unity and global impact are our Strategic Partners of the Crown.
          </p>
        </div>

        {/* Partners Grid */}
        {/* Loading State */}
        {loading && !error && (
          <div className="text-center py-12 w-full col-span-full">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalGold mx-auto mb-4"></div>
            <p className="text-white/90">Loading partners...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50/10 border-l-4 border-red-500 p-4 mb-8 rounded col-span-full">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-200">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-8 mb-16 w-full">
          {finalPartners.map((partner, index) => (
            <div
              key={partner.id || index}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center border border-white/20 shadow-xl hover:bg-white/15 transition-all duration-300 flex flex-col h-full"
            >
              <div className="flex-grow">
                {/* Logo Circle */}
                <div className="w-20 h-20 mx-auto mb-4 bg-white rounded-full flex items-center justify-center border-4 border-royalGold overflow-hidden">
                  {'logoUrl' in partner ? (
                    <img 
                      src={partner.logoUrl} 
                      alt={partner.name}
                      className="w-full h-full object-contain p-2"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = document.createElement('i');
                        fallback.className = `${getLogoClass(partner)} text-royalBlue text-2xl`;
                        target.parentNode?.insertBefore(fallback, target);
                      }}
                    />
                  ) : (
                    <img 
                      src={partner.logo} 
                      alt={partner.name}
                      className="w-full h-full object-contain p-2"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = document.createElement('i');
                        fallback.className = `${getLogoClass(partner)} text-royalBlue text-2xl`;
                        target.parentNode?.insertBefore(fallback, target);
                      }}
                    />
                  )}
                </div>

                {/* Partner Name */}
                <h3 className="text-lg font-bold text-white mb-2">{partner.name}</h3>

                {/* Description */}
                <p className="text-white/80 text-sm mb-4 leading-relaxed">{partner.description}</p>
              </div>

              {/* Visit Website Link - Aligned to bottom */}
              <div className="mt-auto pt-4">
                <a
                  href={partner.website || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-royalGold hover:text-yellow-400 text-sm font-semibold transition-colors duration-300"
                >
                  Visit Website →
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* View All Partners Button */}
        <div className="text-center">
          <a
            href="/partners"
            className="inline-flex items-center px-8 py-4 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-2xl border-2 border-yellow-300 group"
            style={{
              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
              boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5), inset 0 3px 6px rgba(255, 255, 255, 0.4), inset 0 -3px 6px rgba(0, 0, 0, 0.3)'
            }}
          >
            {/* Shining animation overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
            <span className="relative z-10 font-extrabold">
              View All Partners →
            </span>
          </a>
        </div>
      </div>
    </section>
  )
}

export default StrategicPartners
