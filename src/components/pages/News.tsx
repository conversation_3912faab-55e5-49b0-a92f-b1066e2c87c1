'use client';

import React, { useState, useEffect } from 'react';
import { getNewsArticles, getStorageUrl } from '@/lib/firebase';
import { format } from 'date-fns';

import { NewsArticle as FirebaseNewsArticle } from '@/lib/firebase';

// Extend the Firebase NewsArticle with our additional fields
interface NewsArticle extends FirebaseNewsArticle {
  fullImageUrl?: string;
  excerpt: string; // Always defined with a default empty string
  summary?: string; // For backward compatibility
}

const News: React.FC = () => {
  const [featuredArticle, setFeaturedArticle] = useState<NewsArticle | null>(null);
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [categories, setCategories] = useState<string[]>(['all']);

  useEffect(() => {
    let isMounted = true;

    const fetchNews = async () => {
      // Only run on client side
      if (typeof window === 'undefined') return;

      try {
        console.log('Starting to fetch news articles...');
        setLoading(true);
        setError(null);

        console.log('Calling getNewsArticles()...');
        const response = await getNewsArticles();
        console.log('Received response from getNewsArticles:', {
          hasFeatured: !!response.featured,
          articleCount: response.articles.length,
          categories: response.categories
        });
        if (!isMounted) return;

        // Process featured article image if exists
        let processedFeatured: NewsArticle | null = null;
        if (response.featured) {
          try {
            let fullImageUrl: string | undefined = undefined;
            if (response.featured.featuredImage && response.featured.featuredImage.startsWith('http')) {
              fullImageUrl = response.featured.featuredImage;
            } else if (Array.isArray(response.featured.images) && response.featured.images[0] && response.featured.images[0].startsWith('http')) {
              fullImageUrl = response.featured.images[0];
            } else if (response.featured.featuredImagePath) {
              fullImageUrl = await getStorageUrl(response.featured.featuredImagePath, 'news') || undefined;
            } else if (Array.isArray(response.featured.imagesPaths) && response.featured.imagesPaths[0]) {
              fullImageUrl = await getStorageUrl(response.featured.imagesPaths[0], 'news') || undefined;
            } else if (response.featured.imageUrl) {
              fullImageUrl = await getStorageUrl(response.featured.imageUrl, 'news') || undefined;
            }
            processedFeatured = { 
              ...response.featured, 
              fullImageUrl,
              excerpt: response.featured.summary // Map summary to excerpt for backward compatibility
            };
          } catch (err) {
            console.error('Error loading featured image:', err);
            processedFeatured = {
              ...response.featured,
              excerpt: response.featured.summary
            };
          }
        }

        // Process articles to include full image URLs
        const processedArticles: NewsArticle[] = await Promise.all(
          response.articles.map(async (article) => {
            let fullImageUrl: string | undefined = undefined;
            try {
              if (article.featuredImage && article.featuredImage.startsWith('http')) {
                fullImageUrl = article.featuredImage;
              } else if (Array.isArray(article.images) && article.images[0] && article.images[0].startsWith('http')) {
                fullImageUrl = article.images[0];
              } else if (article.featuredImagePath) {
                fullImageUrl = await getStorageUrl(article.featuredImagePath, 'news') || undefined;
              } else if (Array.isArray(article.imagesPaths) && article.imagesPaths[0]) {
                fullImageUrl = await getStorageUrl(article.imagesPaths[0], 'news') || undefined;
              } else if (article.imageUrl) {
                fullImageUrl = await getStorageUrl(article.imageUrl, 'news') || undefined;
              }
              const processedArticle: NewsArticle = {
                ...article,
                fullImageUrl,
                excerpt: article.summary ?? '', // Use summary if available, fallback to empty string
                summary: article.summary // Keep the original summary
              };
              return processedArticle;
            } catch (err) {
              console.error('Error loading image:', err);
              return { 
                ...article, 
                excerpt: article.summary ?? '',
                summary: article.summary
              };
            }
          })
        );

        // Get unique categories from both featured and regular articles
        const allArticles = processedFeatured ? [processedFeatured, ...processedArticles] : processedArticles;
        const categorySet = new Set<string>();
        
        // Add categories to set
        for (const article of allArticles) {
          const category = article?.category;
          if (typeof category === 'string' && category.trim()) {
            categorySet.add(category.toLowerCase());
          }
        }
        
        // Convert set to array
        const uniqueCategories = ['all'];
        const categoryIterator = categorySet.values();
        for (const category of Array.from(categoryIterator)) {
          uniqueCategories.push(category);
        }

        if (isMounted) {
          setFeaturedArticle(processedFeatured);
          setArticles(processedArticles);
          setCategories(prev => [...new Set([...prev, ...uniqueCategories])]);
        }
      } catch (err) {
        console.error('Error fetching news:', err);
        if (isMounted) {
          setError('Failed to load news. Please try again later.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchNews();
    return () => { isMounted = false; };
  }, []);

  const formatDate = (date: Date | { toDate: () => Date }): string => {
    try {
      const dateObj = date instanceof Date ? date : date?.toDate?.() || new Date();
      return format(dateObj, 'MMMM d, yyyy');
    } catch (err) {
      console.error('Error formatting date:', err);
      return '';
    }
  };

  const filteredArticles = articles.filter(article => {
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    const searchTerm = searchQuery.toLowerCase();
    const matchesSearch = !searchQuery || 
      article.title.toLowerCase().includes(searchTerm) ||
      (article.summary?.toLowerCase()?.includes(searchTerm) ?? false);
    return matchesCategory && matchesSearch;
  });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalGold"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="text-red-500 text-lg mb-4">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="px-6 py-2 bg-royalGold text-royalBlue rounded-lg font-medium hover:bg-yellow-500 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/8 via-transparent to-royalBlue/8"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Royal News & Updates
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Stay updated with the latest news and announcements from the Royal Kingdom.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category
                  ? 'bg-royalGold text-white'
                  : 'bg-white text-royalBlue hover:bg-gray-100'
              }`}
            >
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </button>
          ))}
        </div>

        {/* Search */}
        <div className="max-w-md mx-auto mb-12">
          <div className="relative">
            <input
              type="text"
              placeholder="Search news..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-3 pl-12 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-royalGold focus:border-transparent"
            />
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>

        {/* News Grid */}
        {/* Featured Article */}
        {featuredArticle && selectedCategory === 'all' && !searchQuery && featuredArticle.fullImageUrl && (
          <div className="mb-12">
            <h3 className="text-2xl font-bold text-royalBlue mb-6">Featured Story</h3>
            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <div className="md:flex">
                <div className="md:w-1/2 h-64 md:h-auto">
                  {featuredArticle.fullImageUrl ? (
                    <img
                      src={featuredArticle.fullImageUrl}
                      alt={featuredArticle.title}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder-news.jpg';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-royalGold/20 to-royalBlue/20 flex items-center justify-center">
                      <i className="fas fa-image text-4xl text-royalGold/50"></i>
                    </div>
                  )}
                </div>
                <div className="p-8 md:w-1/2 flex flex-col">
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <span className="bg-royalGold/10 text-royalGold px-3 py-1 rounded-full text-xs font-medium">
                      {featuredArticle.category || 'Featured'}
                    </span>
                    <span className="mx-2">•</span>
                    <span>{formatDate(featuredArticle.date)}</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {featuredArticle.title}
                  </h3>
                  <p className="text-gray-600 mb-6 flex-grow">
                    {featuredArticle.excerpt || featuredArticle.summary || 'No excerpt available.'}
                  </p>
                  <a
                    href={`/news/${featuredArticle.id}`}
                    className="inline-flex items-center text-royalGold font-medium hover:text-royalBlue transition-colors self-start"
                  >
                    Read full story
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Regular Articles */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredArticles.map((article) => (
            <div key={article.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <div className="h-48 bg-gray-100 overflow-hidden">
                {article.fullImageUrl ? (
                  <img
                    src={article.fullImageUrl}
                    alt={article.title}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder-news.jpg';
                    }}
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-royalGold/20 to-royalBlue/20 flex items-center justify-center">
                    <i className="fas fa-image text-4xl text-royalGold/50"></i>
                  </div>
                )}
              </div>
              <div className="p-6">
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <span className="bg-royalGold/10 text-royalGold px-3 py-1 rounded-full text-xs font-medium">
                    {article.category || 'News'}
                  </span>
                  <span className="mx-2">•</span>
                  <span>{formatDate(article.date)}</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-gray-600 line-clamp-3 mb-4">
                  {article.summary || 'No summary available.'}
                </p>
                <a
                  href={`/news/${article.id}`}
                  className="inline-flex items-center text-royalGold font-medium hover:text-royalBlue transition-colors"
                >
                  Read more
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </div>
          ))}
        </div>

        {filteredArticles.length === 0 && !loading && (
          <div className="text-center py-12">
            <p className="text-gray-500">No articles found matching your criteria.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default News;
