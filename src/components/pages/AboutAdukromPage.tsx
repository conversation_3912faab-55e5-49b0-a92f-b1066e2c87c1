'use client'

const AboutAdukromPage: React.FC = () => {
  const timelineData = [
    {
      period: "16TH CENTURY",
      title: "Settlement Begins",
      description: "The Aklemede Clan, led by <PERSON><PERSON><PERSON>, is the first to settle the area now known as Adukrom. Boame names the new settlement Asienso between Apirede and Awukugua. Later, <PERSON><PERSON><PERSON> (Adumanuru), a hunter and herbalist from Akyem Kotoku joins, bringing the black stool of the Aduana clan. His influence leads to the town eventually being called Adukrom — Adu's town."
    },
    {
      period: "1730-1733",
      title: "Formation of Akuapem State & Chieftaincy System",
      description: "Adukrom is named the capital of the Nifa Division, one of the key divisions of the Akuapem State. Traditional priesthood governance transitions into a formal chieftaincy with the reign of <PERSON> (1733–1764), the first enstooled chief of Adukrom."
    },
    {
      period: "1772-1843",
      title: "Era of Consolidation & Military Victory",
      description: "Reign of Nana Otutu <PERSON> (Ohene Kwame Donkor) from 1772-1813 marks a defining era of consolidation for <PERSON><PERSON><PERSON>'s authority. <PERSON> (1813-1843) participates in the Akatamanso War (1826)—a historic victory over the Ashanti Empire at Dodowa."
    },
    {
      period: "1843-1900",
      title: "Heroism & Royal Recognition",
      description: "Nana Otutu Opare Ababio I (Aketewa Mpenyinsem) shows great bravery during the Awuna War (1866), rescuing the Okuapehene and killing the Anlo leader Djo Kpantanpklan. As a reward, Adukrom receives sacred items in 1869, including Adongua, a feathered cap, drums, and a palanquin. The Otutu Stool Symbol is adopted: a man standing on thorns to lift a leopard cub—symbolizing courage and royal sacrifice."
    },
    {
      period: "1900-2016",
      title: "Modern Leadership",
      description: "A succession of Otutu Chiefs preserves Adukrom's spiritual and political legacy: Nana Otutu Ababio III (1900–1933), Nana Otutu Kono II (1933–1943), Nana Opare Ababio II (1943–1945), Osuodumgya Otutu Ababio IV (1945–2000) – one of the longest reigning chiefs. From 2002-2016, Nana Otutu Kono leads the town through modern administrative transitions."
    },
    {
      period: "2016-PRESENT",
      title: "Current Leadership",
      description: "Reign of Osuodumgya Otutu Ababio V (Paul Yeboah Konoboa): Current traditional ruler of Adukrom and the spiritual anchor of the Otutu stool. Under his leadership, Adukrom has experienced a renaissance in cultural preservation while embracing economic development initiatives."
    },
    {
      period: "2025",
      title: "Grand Coronation of His Majesty Mpuntuhene Allen Ellison",
      description: "Enstooled as Mpuntuhene – King of Trade, Investment, and Economic Development, His Majesty ushers Adukrom into a global renaissance, bridging tradition with innovation and establishing the Ellison Royal Sovereign Wealth Fund to empower the Kingdom and beyond."
    }
  ]

  const strategicAdvantages = [
    {
      icon: "🌍",
      title: "Elevated Location",
      description: "The Togo Atakora Hills offer natural fortification, cooler climates, and stunning vistas — ideal for eco-tourism, retreats, and royal ceremonies."
    },
    {
      icon: "🚚",
      title: "Trade Connectivity", 
      description: "Sitting on a key transit route, Adukrom links major economic centers, enhancing its role as a burgeoning hub for commerce and logistics in Ghana's Eastern Region."
    },
    {
      icon: "🌿",
      title: "Agricultural Potential",
      description: "Fertile lands and favorable weather conditions make Adukrom a center for sustainable agriculture and agri-business development."
    },
    {
      icon: "⚡",
      title: "Renewable Energy Opportunities",
      description: "The landscape is primed for green energy initiatives, including wind and solar projects aligned with the Kingdom's sustainability goals."
    }
  ]

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"
        />
        <div
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            About Adukrom Kingdom
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            The Crown of Africa - A comprehensive look at the history, heritage, and future of the Adukrom Kingdom.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-16 items-start max-w-7xl mx-auto">
          {/* Content - Left Side (2/3 width) */}
          <div
            className="lg:col-span-2 space-y-8"
          >
            {/* About Adukrom */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-crown text-white text-sm"></i>
                </div>
                <h2 className="text-2xl font-bold text-royalBlue">About Adukrom</h2>
              </div>

              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p>
                  Nestled atop the serene heights of the Togo Atakora Hills, Adukrom commands breathtaking views and an even more remarkable legacy. As the capital of the Okere District Assembly in Ghana's Eastern Region, Adukrom is a town where history breathes through every pathway, and where the future is being shaped by visionary leadership.
                </p>
                
                <p>
                  Positioned along the crucial Ho-Koforidua main trunk road, Adukrom serves as a gateway between Ghana's vibrant eastern corridor and the broader West African trade routes. Its strategic location has long made it a center for commerce, cultural exchange, and governance within the Akuapem State.
                </p>
              </div>
            </div>

            {/* Custodians of Tradition */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalBlue to-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-chess-king text-white text-sm"></i>
                </div>
                <h3 className="text-xl font-bold text-royalBlue">Custodians of Tradition: The Nifahene Stool</h3>
              </div>
              
              <p className="text-gray-700 leading-relaxed">
                Adukrom is renowned as the seat of the revered Nifahene Stool of Akuapem — a symbol of authority, wisdom, and stewardship. The Nifahene plays a critical role in maintaining the balance of leadership within the Akuapem chieftaincy structure, overseeing matters of defense, diplomacy, and community cohesion.
              </p>
              
              <p className="text-gray-700 leading-relaxed mt-3">
                Today, under the reign of His Majesty Mpuntuhene Allen Ellison, this legacy continues — infused with a modern mandate to expand Adukrom's influence through trade, investment, and global partnerships.
              </p>
            </div>

            {/* Sacred Neighborhood */}
            <div className="bg-gradient-to-br from-royalBlue/5 to-royalGold/5 backdrop-blur-lg rounded-xl p-6 border border-royalGold/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-star text-white text-sm"></i>
                </div>
                <h3 className="text-xl font-bold text-royalBlue">A Sacred Neighborhood: The Legacy of Okomfo Anokye</h3>
              </div>
              
              <p className="text-gray-700 leading-relaxed">
                Bordering the community of Awukugua Akuapem, Adukrom shares a deep spiritual and historical connection to Okomfo Anokye, one of the most legendary figures in Ghanaian history. As the co-founder of the Ashanti Empire and a mystic of unparalleled renown, Okomfo Anokye's birthplace adds a profound spiritual significance to the region.
              </p>
              
              <p className="text-gray-700 leading-relaxed mt-3">
                This proximity places Adukrom not only at the heart of political leadership but also within a sacred landscape of African heritage — making it a destination for scholars, historians, and cultural pilgrims alike.
              </p>
            </div>
          </div>

          {/* Image - Right Side (1/3 width) */}
          <div
            className="relative lg:sticky lg:top-24"
          >
            <div className="relative overflow-hidden rounded-2xl shadow-lg group">
              <img
                src="/Website Images/Freedom-and-justice-bright.png"
                alt="Freedom and Justice - Adukrom Kingdom"
                className="w-full h-full object-cover aspect-[3/4] transition-transform duration-500 group-hover:scale-102"
                onError={(e) => {
                  // Fallback if image doesn't exist
                  e.currentTarget.style.display = 'none';
                  const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextElement) {
                    nextElement.style.display = 'flex';
                  }
                }}
              />
              {/* Fallback placeholder */}
              <div className="aspect-[3/4] bg-gradient-to-br from-royalBlue/20 to-royalGold/20 flex items-center justify-center" style={{display: 'none'}}>
                <div className="text-4xl text-royalBlue/30">
                  <i className="fas fa-crown"></i>
                </div>
              </div>

              {/* Subtle overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-royalBlue/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Small royal badge */}
              <div className="absolute top-3 right-3 bg-royalGold/90 backdrop-blur-sm rounded-full p-2 shadow-md">
                <i className="fas fa-crown text-white text-sm"></i>
              </div>
            </div>

            {/* Minimal decorative elements */}
            <div className="absolute -top-1 -right-1 w-8 h-8 bg-gradient-to-br from-royalGold/20 to-yellow-400/20 rounded-full blur-lg"></div>
            <div className="absolute -bottom-1 -left-1 w-10 h-10 bg-gradient-to-tr from-royalBlue/20 to-blue-500/20 rounded-full blur-lg"></div>

            {/* Compact title below image */}
            <div className="mt-4 text-center">
              <h4 className="text-lg font-bold text-royalBlue mb-1">Adukrom Kingdom</h4>
              <p className="text-royalGold font-semibold text-sm">The Crown of Africa</p>
              <p className="text-gray-600 text-xs mt-1">Eastern Region, Ghana</p>
            </div>
          </div>
        </div>

        {/* Geography as Destiny Section */}
        <div
          className="mt-20"
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
              Geography as Destiny: Adukrom's Strategic Advantage
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {strategicAdvantages.map((advantage, index) => (
              <div
                key={index}
                className="bg-white/50 backdrop-blur-lg rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 text-center"
              >
                <div className="text-4xl mb-4">{advantage.icon}</div>
                <h3 className="text-lg font-bold text-royalBlue mb-3">{advantage.title}</h3>
                <p className="text-gray-700 text-sm leading-relaxed">{advantage.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Timeline Section */}
        <div
          className="mt-20"
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
              Timeline of Adukrom Kingdom: A Legacy of Leadership and Resilience
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
              From ancient settlements to modern renaissance - the remarkable journey of Adukrom Kingdom
            </p>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-royalGold to-royalBlue"></div>

            {timelineData.map((item, index) => (
              <div
                key={index}
                className={`relative flex items-center mb-12 ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
              >
                {/* Timeline dot */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-royalGold rounded-full border-4 border-white shadow-lg z-10"></div>

                {/* Content */}
                <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                  <div className="bg-white/50 backdrop-blur-lg rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="text-royalGold font-bold text-sm mb-2">{item.period}</div>
                    <h3 className="text-xl font-bold text-royalBlue mb-3">{item.title}</h3>
                    <p className="text-gray-700 text-sm leading-relaxed">{item.description}</p>
                  </div>
                </div>

                {/* Empty space for the other side */}
                <div className="w-5/12"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutAdukromPage
