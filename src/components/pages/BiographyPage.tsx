'use client'

import { useState, useRef, useEffect } from 'react';


const BiographyPage: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Gallery images data
  const galleryImages = [
    {
      src: "/Website Images/agricultural-practices-ghana.png",
      alt: "Agricultural Practices in Ghana"
    },
    {
      src: "/Website Images/KingAllenEllison.png",
      alt: "His Royal Majesty King <PERSON>"
    },
    {
      src: "/Website Images/Allenlook.jpg",
      alt: "His Royal Majesty King <PERSON>"
    }
  ];

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setSelectedImage(null);
      }
    };

    if (selectedImage) {
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [selectedImage]);
  const handleBack = () => {
    window.history.back();
  };

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Back Button */}
      <button 
        onClick={handleBack}
        className="fixed top-6 left-6 z-[100] px-5 py-2.5 bg-royalBlue text-white rounded-lg hover:bg-royalBlue/90 transition-all duration-200 shadow-lg border-2 border-white/30 hover:border-royalGold/50"
      >
        ← Back
      </button>
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"
        />
        <div
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Biography of His Royal Majesty
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            His Royal Majesty King Allen Ellison, Mpuntuhene of Adukrom - A comprehensive look at the life and legacy of a visionary leader.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-16 items-start max-w-7xl mx-auto">
          {/* Content - Left Side (2/3 width) */}
          <div
            className="lg:col-span-2 space-y-8"
          >
            {/* Full Biography */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="mb-4">
                <h2 className="text-2xl font-bold text-royalBlue">Full Biography</h2>
              </div>

              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p>
                  His Majesty King Allen Ellison, globally known for his visionary leadership, is being enstooled Mpuntuhene of Adukrom, Eastern Region of Ghana—an esteemed royal office dedicated to advancing trade, investment, innovation and economic development for the Kingdom and beyond. A statesman, entrepreneur, humanitarian, and cultural ambassador, King Allen stands at the intersection of African tradition and global transformation.
                </p>
                
                <p>
                  Born in Avon Park, Florida, USA, Allen Ellison has risen from humble beginnings to become one of the most dynamic royal figures of the 21st century. With a Bachelor of Arts degree in Political Science and Business Administration from Florida Southern College, he has spent over two decades driving economic empowerment, financial literacy, and sustainable development across communities in the United States, Asia the Caribbean, and Africa.
                </p>
                
                <p>
                  In 2019, His Majesty was honored with the royal title "Son of the Soil" by His Royal Majesty King Jonathan Danladi Gyet Maude of Nok Kingdom, Nigeria. On August 29, 2025, he will be formally enstooled as Mpuntuhene of Adukrom, a sacred office recognized by the Nifaman Council of the Akuapem State. His coronation will mark a new era of economic leadership, positioning Adukrom as a future-facing Kingdom ready to engage the world through commerce, diplomacy, and innovation.
                </p>
                
                <p>
                  As a Board member of The Bank of Humanity, custodian of the Ellison Royal Sovereign Wealth Fund and Chairman of The Ellison Family Office, His Majesty oversees global initiatives in renewable energy, fintech, education, agriculture, and infrastructure development. His leadership is rooted in the belief that traditional authority can be a powerful force for modern progress—unifying the African diaspora, attracting foreign direct investment, and elevating the continent's narrative on the global stage.
                </p>
                
                <p>
                  King Allen Ellison is also an actor of an award-winning film, published author, businessman, and former U.S. Senatorial candidate. His life and legacy are a living bridge between continents—proof that royalty can be as bold as it is benevolent, as visionary as it is rooted in ancestral truth.
                </p>
              </div>
            </div>

            {/* Vision */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-5 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <h3 className="text-lg font-bold text-royalBlue mb-3">Vision</h3>
              <p className="text-gray-700 leading-relaxed">
                To establish Ghana as a beacon of cultural preservation and sustainable development in Africa, where traditional wisdom and modern innovation harmoniously coexist to create prosperity for all citizens.
              </p>
            </div>

            {/* Mission */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-5 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <h3 className="text-lg font-bold text-royalBlue mb-3">Mission</h3>
              <p className="text-gray-700 leading-relaxed">
                To lead with wisdom, serve with humility, and govern with justice, ensuring that every citizen has the opportunity to reach their full potential through sustainable development and cultural preservation.
              </p>
            </div>
          </div>

          {/* Profile Image - Right Side (1/3 width) */}
          <div
            className="relative lg:sticky lg:top-24"
          >
            <div className="relative overflow-hidden rounded-2xl shadow-lg group">
              <img
                src="/Website Images/Allen-Ellison-Profile-scaled-1.png"
                alt="His Royal Majesty King Allen Ellison"
                className="w-full h-full object-cover aspect-[3/4] transition-transform duration-500 group-hover:scale-102"
                onError={(e) => {
                  // Fallback if image doesn't exist
                  e.currentTarget.style.display = 'none';
                  const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextElement) {
                    nextElement.style.display = 'flex';
                  }
                }}
              />
              {/* Fallback placeholder */}
              <div className="aspect-[3/4] bg-gradient-to-br from-royalBlue/20 to-royalGold/20 flex items-center justify-center" style={{display: 'none'}}>
                <div className="text-4xl text-royalBlue/30">
                  Image
                </div>
              </div>

              {/* Subtle overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-royalBlue/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>


            </div>

            {/* Minimal decorative elements */}
            <div className="absolute -top-1 -right-1 w-8 h-8 bg-gradient-to-br from-royalGold/20 to-yellow-400/20 rounded-full blur-lg"></div>
            <div className="absolute -bottom-1 -left-1 w-10 h-10 bg-gradient-to-tr from-royalBlue/20 to-blue-500/20 rounded-full blur-lg"></div>

            {/* Compact title below image */}
            <div className="mt-4 text-center">
              <h4 className="text-lg font-bold text-royalBlue mb-1">His Royal Majesty</h4>
              <p className="text-royalGold font-semibold text-sm">King Allen Ellison</p>
              <p className="text-gray-600 text-xs mt-1">Mpuntuhene of Adukrom</p>
            </div>

            {/* Gallery Section */}
            <div className="mt-6">
              <h3 className="text-lg font-bold text-royalBlue mb-4 text-center">Gallery</h3>
              <div className="grid grid-cols-3 gap-3">
                {galleryImages.map((image, index) => (
                  <div 
                    key={index}
                    className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group hover:scale-103 transition-transform duration-200"
                    onClick={() => setSelectedImage(image.src)}
                  >
                    <div className="absolute inset-0 border-2 border-royalGold/40 rounded-lg group-hover:border-royalGold transition-all duration-300 z-10 pointer-events-none"></div>
                    <img
                      src={image.src}
                      alt={image.alt}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-br from-royalBlue/10 to-royalGold/10 flex items-center justify-center hidden">
                      <i className="fas fa-image text-2xl text-gray-400"></i>
                    </div>
                    {/* Gold foil overlay effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-royalGold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                ))}
              </div>

              {/* Purpose Statement */}
              <div className="mt-6 bg-white/50 backdrop-blur-lg rounded-xl p-5 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
                <h3 className="text-lg font-bold text-royalBlue mb-3">Purpose Statement</h3>
                
                <p className="text-gray-700 leading-relaxed italic">
                  Our purpose is to preserve tradition while creating opportunity — advancing trade, investment, and diplomacy to uplift communities, bridge Africa with the world, and ignite a lasting legacy of empowerment through The Rebirth of Adukrom.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/90 backdrop-blur-sm z-40 flex items-center justify-center p-4 opacity-0 animate-fade-in"
          onClick={() => setSelectedImage(null)}
          style={{
            animation: 'fadeIn 0.3s ease-out forwards'
          }}
        >
          <div className="relative max-w-4xl w-full max-h-[90vh]" ref={modalRef}>
            <button 
              onClick={(e) => {
                e.stopPropagation();
                setSelectedImage(null);
              }}
              className="absolute -top-12 right-0 text-white hover:text-royalGold transition-colors duration-200 z-50"
              aria-label="Close modal"
            >
              Close
            </button>
            <div
              className="relative h-full scale-90 opacity-0"
              style={{
                animation: 'scaleIn 0.2s 0.1s ease-out forwards'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={selectedImage}
                alt="Enlarged view"
                className="max-w-full max-h-[80vh] mx-auto rounded-lg border-4 border-royalGold/50 shadow-2xl"
              />
            </div>
          </div>
        </div>
      )}
    </section>
  )
}

export default BiographyPage
