'use client'

import { useState, useEffect } from 'react';
import { 
  GalleryImage, 
  getDirectStorageImages,
  onGalleryImagesUpdate 
} from '@/lib/firebase';

const RoyalGalleryPage: React.FC = () => {
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  useEffect(() => {
    let isMounted = true;

    const fetchImages = async () => {
      // Only run on client side
      if (typeof window === 'undefined') return;

      try {
        setLoading(true);
        setError(null);

        // Fetch images directly from storage
        const images = await getDirectStorageImages();
        if (!isMounted) return;
        
        console.log('Fetched images:', images);
        
        // Sort by filename (you can adjust the sorting logic as needed)
        const sortedImages = [...images].sort((a, b) => 
          (a.title || '').localeCompare(b.title || '')
        );
        
        setGalleryImages(sortedImages);
        
      } catch (err) {
        console.error('Failed to fetch gallery images:', err);
        if (isMounted) {
          setError('Failed to load gallery images. Please try again later.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchImages();
    
    return () => {
      isMounted = false;
    };
  }, []);
  
  // Helper function to sort images by date (newest first)
  const sortImagesByDate = (images: GalleryImage[]): GalleryImage[] => {
    return [...images].sort((a, b) => {
      const getDate = (date: Date | { toDate: () => Date } | undefined): Date => {
        if (!date) return new Date(0);
        return date instanceof Date ? date : date.toDate();
      };
      
      const dateA = getDate(a.uploadedAt);
      const dateB = getDate(b.uploadedAt);
      return dateB.getTime() - dateA.getTime();
    });
  };

  return (
    <div className="pt-20 pb-20 min-h-screen relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>
      
      <div className="container mx-auto px-4 relative z-10 py-8">
        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalGold"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-10">
            <p className="text-red-500">{error}</p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-4 px-6 py-2 bg-royalGold text-royalBlue rounded-lg font-medium hover:bg-yellow-500 transition-colors"
            >
              Retry
            </button>
          </div>
        )}

        {/* Image Grid */}
        {!loading && !error && (
          <>
            {galleryImages.length === 0 ? (
              <div className="text-center py-20">
                <p className="text-gray-200">No images found in the gallery.</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {galleryImages.map((image, index) => (
                  <div 
                    key={image.id}
                    className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group transition-transform hover:scale-102"
                    onClick={() => setSelectedImage(image)}
                  >
                    <div className="absolute inset-0 border-2 border-royalGold/40 rounded-lg group-hover:border-royalGold transition-all duration-300 z-10 pointer-events-none"></div>
                    <div className="relative w-full h-full">
                      {image.url ? (
                        <img
                          src={image.url}
                          alt={image.title || 'Gallery image'}
                          className="w-full h-full object-cover rounded-lg transition-transform duration-300 group-hover:scale-105"
                          onError={(e) => {
                            console.error('Error loading image:', image.url);
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder-image.jpg';
                          }}
                          loading="lazy"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center rounded-lg">
                          <span className="text-gray-500">Image not found</span>
                          <p className="text-xs text-center">Loading image...</p>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue/10 to-royalGold/10 flex items-center justify-center hidden">
                        <i className="fas fa-image text-4xl text-royalGold/50"></i>
                      </div>
                      {/* Image info overlay */}
                      {(image.title || image.description) && (
                        <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          {image.title && <p className="font-medium text-sm truncate">{image.title}</p>}
                          {image.description && <p className="text-xs opacity-90 truncate">{image.description}</p>}
                        </div>
                      )}
                      {/* Gold foil overlay effect */}
                      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-royalGold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Image Modal */}
      {selectedImage && (
          <div 
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div className="relative max-w-4xl w-full max-h-[90vh]">
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedImage(null);
                }}
                className="absolute -top-10 right-0 text-white hover:text-royalGold transition-colors duration-200 z-10"
                aria-label="Close modal"
              >
                <i className="fas fa-times text-2xl"></i>
              </button>
              <div
                className="relative h-full"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                <div className="text-center">
                <img
                  src={selectedImage.url || ''}
                  alt={selectedImage.title || 'Enlarged view'}
                  className="max-w-full max-h-[70vh] mx-auto rounded-lg border-4 border-royalGold/50 shadow-2xl"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                  loading="eager"
                />
                <div className="hidden items-center justify-center h-64 bg-gray-800 rounded-lg">
                  <i className="fas fa-image text-4xl text-gray-500"></i>
                </div>
                {(selectedImage.title || selectedImage.description) && (
                  <div className="mt-4 text-white text-left max-w-2xl mx-auto">
                    {selectedImage.title && <h3 className="text-xl font-bold mb-2">{selectedImage.title}</h3>}
                    {selectedImage.description && <p className="text-gray-300">{selectedImage.description}</p>}
                  </div>
                )}
              </div>
              </div>
            </div>
          </div>
        )}

    </div>
  );
};

export default RoyalGalleryPage;
