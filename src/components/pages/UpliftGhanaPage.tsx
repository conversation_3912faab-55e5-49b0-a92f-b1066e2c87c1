'use client'

import React from 'react'
import Header from '../layout/Header'
import Footer from '../layout/Footer'

const UpliftGhanaPage: React.FC = () => {
  const corePillars = [
    {
      title: 'Education for Empowerment',
      icon: 'fas fa-graduation-cap',
      gradient: 'from-blue-500 to-blue-700',
      items: [
        'Renovation of 11 high schools across the Eastern Region',
        'Deployment of digital learning labs and teacher development programs',
        'Scholarships and leadership academies for underserved youth'
      ]
    },
    {
      title: 'Entrepreneurship & Local Industry',
      icon: 'fas fa-rocket',
      gradient: 'from-green-500 to-green-700',
      items: [
        'Microgrant programs for youth and women entrepreneurs',
        'Agro-processing and manufacturing cluster development',
        'Royal Business Incubator in Adukrom for startups and artisans'
      ]
    },
    {
      title: 'Sustainable Infrastructure & Energy',
      icon: 'fas fa-solar-panel',
      gradient: 'from-yellow-500 to-orange-600',
      items: [
        'Smart community development projects',
        'Expansion of solar-powered microgrids and eco-housing units',
        'Modernization of the Adukrom Business District as a model economic hub'
      ]
    },
    {
      title: 'Cultural Renaissance & Diaspora Engagement',
      icon: 'fas fa-palette',
      gradient: 'from-purple-500 to-pink-600',
      items: [
        'Cultural heritage preservation through digital archiving and festivals',
        'Diaspora "Return to Invest" campaign under royal leadership',
        'Arts and fashion collaborations showcasing Ghana\'s identity to the world'
      ]
    },
    {
      title: 'Health & Human Services',
      icon: 'fas fa-heartbeat',
      gradient: 'from-red-500 to-red-700',
      items: [
        'Community clinics and maternal care initiatives',
        'Nutrition and clean water access in rural districts',
        'Royal Mobile Health Caravans for outreach'
      ]
    }
  ]

  const joinOptions = [
    {
      title: 'Become a Founding Partner',
      description: 'Join our network of strategic partners and help shape the future of Ghana.',
      icon: 'fas fa-handshake',
      gradient: 'from-royalGold to-yellow-500'
    },
    {
      title: 'Sponsor a Project',
      description: 'Support specific initiatives aligned with your organization\'s values and goals.',
      icon: 'fas fa-donate',
      gradient: 'from-green-500 to-emerald-600'
    },
    {
      title: 'Engage Your Diaspora Network',
      description: 'Connect Ghanaians abroad with opportunities to contribute to national development.',
      icon: 'fas fa-globe-africa',
      gradient: 'from-blue-500 to-indigo-600'
    },
    {
      title: 'Attend the Uplift Ghana Royal Summit 2025',
      description: 'Join leaders and changemakers at our inaugural summit to chart Ghana\'s future.',
      icon: 'fas fa-users',
      gradient: 'from-purple-500 to-violet-600'
    }
  ]

  return (
    <main className="min-h-screen">
      <Header />
      <div className="pt-20">
        {/* Hero Section with Ghana Flag Background */}
        <section className="py-20 relative overflow-hidden">
          {/* Ghana Flag Background */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-gradient-to-br from-red-600 via-yellow-400 to-green-600 opacity-20"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
          </div>
          
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="royal-pattern w-full h-full"></div>
          </div>

          {/* Background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/20 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-green-500/20 to-transparent rounded-full blur-2xl"></div>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            {/* Back to Home Button */}
            <div className="mb-8">
              <a
                href="/"
                className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-md border border-white/30 text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300"
              >
                <i className="fas fa-arrow-left mr-2"></i>
                Back to Home
              </a>
            </div>

            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6 leading-tight">
                <span className="block">Uplift Ghana</span>
                <span className="block text-royalGold text-2xl md:text-3xl lg:text-4xl mt-4 font-light">
                  A Royal Initiative for National Prosperity, Inclusion, and Innovation
                </span>
              </h1>
              <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
              
              <a
                href="#join-movement"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-lg"
              >
                <i className="fas fa-rocket mr-2"></i>
                Join the Movement
              </a>
            </div>
          </div>
        </section>

        {/* Overview Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-royalBlue mb-6">Overview</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>
              <p className="text-lg text-gray-700 leading-relaxed mb-8">
                Uplift Ghana is a visionary initiative launched under the patronage of His Majesty Mpuntuhene Allen Ellison, designed to accelerate socio-economic transformation across Ghana through strategic investments in education, entrepreneurship, infrastructure, and cultural empowerment.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                Rooted in the values of unity, sustainability, and Pan-African excellence, Uplift Ghana is a call to action—uniting public and private sectors, diaspora leaders, global investors, and communities to build a thriving, future-ready Ghana.
              </p>
            </div>

            {/* Mission & Vision */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
              <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-8 border border-white/50 shadow-xl">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-royalBlue to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i className="fas fa-bullseye text-white text-2xl"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-royalBlue">Mission Statement</h3>
                </div>
                <p className="text-gray-700 leading-relaxed text-center">
                  To empower communities across Ghana by promoting inclusive development, nurturing local talent, and mobilizing global partnerships that foster economic opportunity and elevate national pride.
                </p>
              </div>

              <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-8 border border-white/50 shadow-xl">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-royalGold to-yellow-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i className="fas fa-eye text-white text-2xl"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-royalBlue">Vision Statement</h3>
                </div>
                <p className="text-gray-700 leading-relaxed text-center">
                  A Ghana where every citizen has the tools, resources, and platform to rise—economically, educationally, and culturally—under the banner of unity, innovation, and royal-led advancement.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Core Pillars Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>

          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="royal-pattern w-full h-full"></div>
          </div>

          {/* Background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/20 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4">Core Pillars of Uplift Ghana</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
              <p className="text-white/90 max-w-3xl mx-auto text-lg">
                Five strategic pillars designed to transform Ghana through comprehensive development initiatives.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {corePillars.map((pillar, index) => (
                <div
                  key={index}
                  className="group bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300"
                >
                  <div className="text-center mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-br ${pillar.gradient} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <i className={`${pillar.icon} text-white text-2xl`}></i>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{pillar.title}</h3>
                  </div>

                  <ul className="space-y-3">
                    {pillar.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start text-white/90 text-sm">
                        <div className="w-2 h-2 bg-royalGold rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>

                  {/* Hover effect overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-royalGold/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"></div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Leadership Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-royalBlue mb-6">Led By</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>

              <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-8 border border-white/50 shadow-xl mb-8">
                <h3 className="text-2xl font-bold text-royalBlue mb-4">The Royal Palace of Adukrom</h3>
                <p className="text-lg text-gray-700 mb-6">
                  Under the leadership of His Majesty Mpuntuhene Allen Ellison
                </p>
                <p className="text-gray-700 leading-relaxed">
                  In strategic alliance with the Ellison Royal Sovereign Wealth Fund, the Nifaman Council of the Akuapem State, and national and international development partners.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Join the Movement Section */}
        <section id="join-movement" className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>

          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="royal-pattern w-full h-full"></div>
          </div>

          {/* Background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/20 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4">Join the Movement</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
              <p className="text-white/90 max-w-3xl mx-auto text-lg">
                Whether you're an investor, institution, policymaker, or proud Ghanaian, Uplift Ghana offers a platform to create lasting impact.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
              {joinOptions.map((option, index) => (
                <div
                  key={index}
                  className="group bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300"
                >
                  <div className="text-center mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-br ${option.gradient} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <i className={`${option.icon} text-white text-2xl`}></i>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{option.title}</h3>
                  </div>

                  <p className="text-white/90 text-center leading-relaxed">
                    {option.description}
                  </p>

                  {/* Hover effect overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-royalGold/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"></div>
                </div>
              ))}
            </div>

            {/* Contact CTA */}
            <div className="text-center">
              <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl max-w-2xl mx-auto">
                <h3 className="text-2xl font-bold text-white mb-4">Contact Us to Get Involved</h3>
                <p className="text-white/90 mb-6 leading-relaxed">
                  Ready to be part of Ghana's transformation? Reach out to learn more about partnership opportunities and how you can contribute to this royal initiative.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="#contact"
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <i className="fas fa-envelope mr-2"></i>
                    Contact Us
                  </a>
                  <a
                    href="/rsvp"
                    className="inline-flex items-center px-8 py-4 bg-white/20 backdrop-blur-md border border-white/30 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <i className="fas fa-calendar-check mr-2"></i>
                    RSVP for Summit 2025
                  </a>
                </div>
              </div>
            </div>

            {/* Footer Branding */}
            <div className="text-center mt-16 pt-8 border-t border-white/20">
              <p className="text-white/80 text-lg font-semibold">
                Uplift Ghana | Kingdom of Adukrom | The Crown of Africa
              </p>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </main>
  )
}

export default UpliftGhanaPage
