'use client';

import React, { useState } from 'react';

const Streaming = () => {
  const [isLive, setIsLive] = useState(true);
  const [isChatOpen, setIsChatOpen] = useState(false);
  
  const upcomingStreams = [
    {
      id: 1,
      title: 'Royal Coronation Ceremony',
      date: 'June 25, 2025',
      time: '10:00 AM GMT',
      description: 'Live broadcast of the grand coronation ceremony of His Royal Majesty.'
    },
    {
      id: 2,
      title: 'Cultural Festival Highlights',
      date: 'June 26, 2025',
      time: '2:00 PM GMT',
      description: 'Highlights from the annual cultural festival showcasing Adukrom heritage.'
    },
    {
      id: 3,
      title: 'Royal Address to the Nation',
      date: 'June 27, 2025',
      time: '6:00 PM GMT',
      description: 'His Royal Majesty addresses the nation with an important message.'
    }
  ];

  return (
    <section className="py-12 px-4 sm:px-6 lg:px-8 bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            {isLive ? 'Live Now' : 'Event Streaming'}
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-gray-300 max-w-2xl mx-auto">
            {isLive 
              ? 'Watch the live broadcast of royal events and ceremonies.'
              : 'Check the schedule for upcoming live streams.'}
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Video Player */}
          <div className={`${isChatOpen ? 'lg:w-2/3' : 'w-full'} bg-black rounded-xl overflow-hidden shadow-2xl`}>
            <div className="relative pt-[56.25%]">
              <div className="absolute inset-0 flex items-center justify-center bg-black">
                {isLive ? (
                  <div className="text-center p-4">
                    <div className="inline-flex items-center bg-red-600 text-white text-sm font-bold px-4 py-1 rounded-full mb-4">
                      <span className="flex h-3 w-3 mr-2">
                        <span className="animate-ping absolute inline-flex h-3 w-3 rounded-full bg-red-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                      </span>
                      LIVE NOW
                    </div>
                    <h3 className="text-2xl font-bold mb-2">Royal Coronation Ceremony</h3>
                    <p className="text-gray-300 mb-4">June 25, 2025</p>
                  </div>
                ) : (
                  <div className="text-center p-8">
                    <div className="text-4xl mb-4">🔴</div>
                    <h3 className="text-2xl font-bold mb-2">Stream Starting Soon</h3>
                    <p className="text-gray-300">The next live stream will begin shortly</p>
                  </div>
                )}
              </div>
            </div>
            <div className="p-4 bg-gray-800">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold">
                    {isLive ? 'Royal Coronation Ceremony' : 'Live Stream'}
                  </h3>
                  {isLive && (
                    <p className="text-sm text-gray-300">
                      Started at 10:00 AM GMT • {viewerCount.toLocaleString()} watching
                    </p>
                  )}
                </div>
                <button 
                  onClick={() => setIsChatOpen(!isChatOpen)}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center"
                >
                  <span className="mr-2">💬</span>
                  {isChatOpen ? 'Hide Chat' : 'Show Chat'}
                </button>
              </div>
            </div>
          </div>

          {/* Chat or Upcoming Streams */}
          <div className={`${isChatOpen ? 'lg:w-1/3' : 'hidden lg:block lg:w-1/3'} bg-gray-800 rounded-xl overflow-hidden shadow-xl`}>
            {isChatOpen ? (
              <div className="h-full flex flex-col">
                <div className="p-4 border-b border-gray-700">
                  <h3 className="font-bold">Live Chat</h3>
                </div>
                <div className="flex-1 p-4 overflow-y-auto">
                  <div className="space-y-4">
                    {chatMessages.map((message, index) => (
                      <div key={index} className="flex">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center text-sm font-bold mr-3">
                          {message.user.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="font-bold text-sm">{message.user}</div>
                          <div className="text-sm">{message.text}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="p-4 border-t border-gray-700">
                  <div className="flex">
                    <input
                      type="text"
                      placeholder="Send a message..."
                      className="flex-1 bg-gray-700 text-white rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-royalGold"
                    />
                    <button className="bg-royalGold text-white px-4 rounded-r-lg hover:bg-yellow-500">
                      Send
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-6">
                <h3 className="text-xl font-bold mb-4">Upcoming Streams</h3>
                <div className="space-y-4">
                  {upcomingStreams.map((stream) => (
                    <div key={stream.id} className="bg-gray-700/50 p-4 rounded-lg">
                      <div className="text-sm text-royalGold font-medium">
                        {stream.date} • {stream.time}
                      </div>
                      <h4 className="font-bold text-lg mb-1">{stream.title}</h4>
                      <p className="text-sm text-gray-300">{stream.description}</p>
                      <button className="mt-3 text-sm text-royalGold hover:underline">
                        Set Reminder
                      </button>
                    </div>
                  ))}
                </div>
                <div className="mt-6 p-4 bg-gray-700/30 rounded-lg">
                  <h4 className="font-bold mb-2">Get Notified</h4>
                  <p className="text-sm text-gray-300 mb-3">
                    Never miss a live stream. Get notified when we go live.
                  </p>
                  <div className="flex">
                    <input
                      type="email"
                      placeholder="Your email"
                      className="flex-1 bg-gray-600 text-white rounded-l-lg px-4 py-2 text-sm focus:outline-none"
                    />
                    <button className="bg-royalGold text-white px-4 text-sm rounded-r-lg hover:bg-yellow-500">
                      Subscribe
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

// Mock data for chat messages
const viewerCount = 12453;
const chatMessages = [
  { user: 'Kwame', text: 'This is amazing! The ceremony is beautiful.' },
  { user: 'Ama', text: 'The traditional dances are incredible!' },
  { user: 'Kofi', text: 'From which part of Ghana is this dance from?' },
  { user: 'Esi', text: 'The colors are so vibrant!' },
  { user: 'Yaw', text: 'Long live the King!' },
];

export default Streaming;
