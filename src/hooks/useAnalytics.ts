import { useState, useEffect } from 'react';
import { getAnalytics, isSupported, logEvent } from 'firebase/analytics';
import { getAuth } from 'firebase/auth';

interface AnalyticsData {
  activeUsers: number;
  totalUsers: number;
  pageViews: number;
  sessions: number;
  avgSessionDuration: number;
  bounceRate: number;
  trafficSources: {
    direct: number;
    organic: number;
    referral: number;
    social: number;
    email: number;
    paid: number;
  };
  topPages: Array<{
    pageTitle: string;
    pagePath: string;
    pageViews: number;
    uniquePageViews: number;
    avgTimeOnPage: number;
    entrances: number;
    bounceRate: number;
  }>;
  userDemographics: {
    countries: Array<{
      country: string;
      users: number;
      newUsers: number;
      sessions: number;
    }>;
    cities: Array<{
      city: string;
      country: string;
      users: number;
    }>;
    languages: Array<{
      language: string;
      users: number;
    }>;
  };
  devices: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  referrers: Array<{
    source: string;
    medium: string;
    sessions: number;
    newUsers: number;
    bounceRate: number;
    pagesPerSession: number;
    avgSessionDuration: number;
  }>;
  realTime: {
    activeUsers: number;
    activePages: Array<{
      pageTitle: string;
      activeUsers: number;
      pagePath: string;
    }>;
    topReferrers: Array<{
      url: string;
      pageViews: number;
    }>;
    topLocations: Array<{
      country: string;
      city?: string;
      activeUsers: number;
    }>;
  };
}

const useAnalytics = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<Partial<AnalyticsData>>({});
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Mock data for development
  const mockData: AnalyticsData = {
    activeUsers: 42,
    totalUsers: 1250,
    pageViews: 8450,
    sessions: 3200,
    avgSessionDuration: 156,
    bounceRate: 0.58,
    trafficSources: {
      direct: 45,
      organic: 30,
      referral: 15,
      social: 7,
      email: 2,
      paid: 1,
    },
    topPages: [
      {
        pageTitle: 'Home',
        pagePath: '/',
        pageViews: 2500,
        uniquePageViews: 1800,
        avgTimeOnPage: 120,
        entrances: 1500,
        bounceRate: 0.65,
      },
      {
        pageTitle: 'About Us',
        pagePath: '/about',
        pageViews: 1800,
        uniquePageViews: 1500,
        avgTimeOnPage: 180,
        entrances: 800,
        bounceRate: 0.45,
      },
      {
        pageTitle: 'Services',
        pagePath: '/services',
        pageViews: 1650,
        uniquePageViews: 1400,
        avgTimeOnPage: 210,
        entrances: 700,
        bounceRate: 0.38,
      },
    ],
    userDemographics: {
      countries: [
        { country: 'United States', users: 800, newUsers: 450, sessions: 1200 },
        { country: 'United Kingdom', users: 250, newUsers: 120, sessions: 400 },
        { country: 'Canada', users: 200, newUsers: 90, sessions: 350 },
      ],
      cities: [
        { city: 'New York', country: 'United States', users: 320 },
        { city: 'London', country: 'United Kingdom', users: 180 },
        { city: 'Toronto', country: 'Canada', users: 150 },
      ],
      languages: [
        { language: 'en', users: 1000 },
        { language: 'es', users: 150 },
        { language: 'fr', users: 100 },
      ],
    },
    devices: {
      desktop: 65,
      mobile: 30,
      tablet: 5,
    },
    referrers: [
      {
        source: 'google',
        medium: 'organic',
        sessions: 1200,
        newUsers: 800,
        bounceRate: 0.45,
        pagesPerSession: 3.2,
        avgSessionDuration: 180,
      },
      {
        source: 'facebook.com',
        medium: 'social',
        sessions: 350,
        newUsers: 200,
        bounceRate: 0.55,
        pagesPerSession: 2.8,
        avgSessionDuration: 150,
      },
      {
        source: 'example.com',
        medium: 'referral',
        sessions: 200,
        newUsers: 100,
        bounceRate: 0.35,
        pagesPerSession: 3.5,
        avgSessionDuration: 200,
      },
    ],
    realTime: {
      activeUsers: 42,
      activePages: [
        { pageTitle: 'Home', activeUsers: 20, pagePath: '/' },
        { pageTitle: 'About Us', activeUsers: 12, pagePath: '/about' },
        { pageTitle: 'Services', activeUsers: 10, pagePath: '/services' },
      ],
      topReferrers: [
        { url: 'google.com', pageViews: 15 },
        { url: 'facebook.com', pageViews: 8 },
        { url: 'example.com', pageViews: 5 },
      ],
      topLocations: [
        { country: 'United States', city: 'New York', activeUsers: 20 },
        { country: 'United Kingdom', city: 'London', activeUsers: 12 },
        { country: 'Canada', city: 'Toronto', activeUsers: 10 },
      ],
    },
  };

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // In a real implementation, you would fetch this data from your backend
      // const response = await fetch('/api/analytics');
      // const result = await response.json();
      
      // For now, using mock data
      setData(mockData);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching analytics data:', err);
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  // Track page view
  const trackPageView = (pageTitle: string, pagePath: string) => {
    isSupported().then(yes => {
      if (yes) {
        const analytics = getAnalytics();
        logEvent(analytics, 'page_view', {
          page_title: pageTitle,
          page_path: pagePath,
          page_location: window.location.href,
        });
      }
    });
  };

  // Track custom event
  const trackEvent = (eventName: string, eventParams: Record<string, any> = {}) => {
    isSupported().then(yes => {
      if (yes) {
        const analytics = getAnalytics();
        logEvent(analytics, eventName, eventParams);
      }
    });
  };

  // Track user sign in
  const trackSignIn = (method: string) => {
    trackEvent('sign_in', { method });
  };

  // Track user sign up
  const trackSignUp = (method: string) => {
    trackEvent('sign_up', { method });
  };

  // Track button click
  const trackButtonClick = (buttonName: string, pagePath: string) => {
    trackEvent('button_click', {
      button_name: buttonName,
      page_path: pagePath,
    });
  };

  // Track form submission
  const trackFormSubmit = (formName: string, formData?: Record<string, any>) => {
    trackEvent('form_submit', {
      form_name: formName,
      ...(formData && { form_data: formData }),
    });
  };

  // Track download
  const trackDownload = (fileName: string, fileType: string) => {
    trackEvent('file_download', {
      file_name: fileName,
      file_type: fileType,
    });
  };

  // Track external link click
  const trackExternalLink = (url: string, linkText: string) => {
    trackEvent('external_link_click', {
      link_url: url,
      link_text: linkText,
      source_page: window.location.pathname,
    });
  };

  // Track search
  const trackSearch = (searchTerm: string, resultsCount: number) => {
    trackEvent('search', {
      search_term: searchTerm,
      results_count: resultsCount,
    });
  };

  // Track video interaction
  const trackVideoInteraction = (videoTitle: string, action: string, value?: any) => {
    trackEvent('video_interaction', {
      video_title: videoTitle,
      action,
      ...(value && { value }),
    });
  };

  // Track social share
  const trackSocialShare = (platform: string, contentTitle: string) => {
    trackEvent('social_share', {
      platform,
      content_title: contentTitle,
      content_url: window.location.href,
    });
  };

  // Track error
  const trackError = (error: Error, context: Record<string, any> = {}) => {
    trackEvent('error', {
      error_message: error.message,
      error_stack: error.stack,
      ...context,
    });
  };

  // Initialize
  useEffect(() => {
    fetchAnalyticsData();

    // Set up auto-refresh every 5 minutes
    const intervalId = setInterval(fetchAnalyticsData, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, []);

  return {
    loading,
    error,
    data: data as AnalyticsData,
    lastUpdated,
    refresh: fetchAnalyticsData,
    trackPageView,
    trackEvent,
    trackSignIn,
    trackSignUp,
    trackButtonClick,
    trackFormSubmit,
    trackDownload,
    trackExternalLink,
    trackSearch,
    trackVideoInteraction,
    trackSocialShare,
    trackError,
  };
};

export default useAnalytics;
