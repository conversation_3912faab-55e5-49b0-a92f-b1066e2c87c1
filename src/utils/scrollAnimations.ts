// Scroll-triggered animations utility
'use client'

export const initScrollAnimations = (): (() => void) => {
  if (typeof window === 'undefined') return () => {}

  const observerOptions: IntersectionObserverInit = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement

        // Add visible class for scroll animations
        if (element.classList.contains('scroll-fade-in')) {
          element.classList.add('visible')
        }

        if (element.classList.contains('scroll-slide-left')) {
          element.classList.add('visible')
        }

        if (element.classList.contains('scroll-slide-right')) {
          element.classList.add('visible')
        }

        // Add staggered animation delays for multiple elements
        const siblings = element.parentElement?.children
        if (siblings) {
          Array.from(siblings).forEach((sibling, index) => {
            if (sibling.classList.contains('scroll-fade-in') ||
                sibling.classList.contains('scroll-slide-left') ||
                sibling.classList.contains('scroll-slide-right')) {
              setTimeout(() => {
                (sibling as HTMLElement).classList.add('visible')
              }, index * 100)
            }
          })
        }

        // Unobserve the element after animation triggers
        observer.unobserve(entry.target)
      }
    })
  }, observerOptions)

  // Observe all elements with scroll animation classes
  const scrollElements = document.querySelectorAll(
    '.scroll-fade-in, .scroll-slide-left, .scroll-slide-right'
  )

  scrollElements.forEach((element) => {
    observer.observe(element)
  })

  // Cleanup function
  return () => {
    observer.disconnect()
  }
}

// Initialize animations when DOM is ready
export const setupScrollAnimations = (): void => {
  if (typeof window === 'undefined') return

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      initScrollAnimations()
    })
  } else {
    initScrollAnimations()
  }
}

// Add floating animation to elements
export const addFloatingAnimation = (selector: string): void => {
  if (typeof window === 'undefined') return

  const elements = document.querySelectorAll(selector)
  elements.forEach((element, index) => {
    const htmlElement = element as HTMLElement
    htmlElement.style.animationDelay = `${index * 0.5}s`
    htmlElement.classList.add('animate-float')
  })
}

// Add shimmer effect to buttons
export const addShimmerEffect = (selector: string): void => {
  if (typeof window === 'undefined') return

  const elements = document.querySelectorAll(selector)
  elements.forEach((element) => {
    element.addEventListener('mouseenter', () => {
      element.classList.add('animate-shimmer')
    })
    
    element.addEventListener('mouseleave', () => {
      element.classList.remove('animate-shimmer')
    })
  })
}

// Staggered animation for grid items
export const staggerGridAnimation = (containerSelector: string, itemSelector: string): void => {
  if (typeof window === 'undefined') return

  const container = document.querySelector(containerSelector)
  if (!container) return

  const items = container.querySelectorAll(itemSelector)
  items.forEach((item, index) => {
    const htmlItem = item as HTMLElement
    htmlItem.style.animationDelay = `${index * 0.1}s`
    htmlItem.classList.add('animate-fade-in-up')
  })
}

// Number counting animation
export const animateNumbers = (selector: string): void => {
  if (typeof window === 'undefined') return

  const elements = document.querySelectorAll(selector)
  
  elements.forEach((element) => {
    const htmlElement = element as HTMLElement
    const finalValue = parseInt(htmlElement.textContent || '0')
    let currentValue = 0
    const increment = finalValue / 60 // 60 frames for 1 second at 60fps
    
    const updateNumber = () => {
      currentValue += increment
      if (currentValue >= finalValue) {
        htmlElement.textContent = finalValue.toString().padStart(2, '0')
      } else {
        htmlElement.textContent = Math.floor(currentValue).toString().padStart(2, '0')
        requestAnimationFrame(updateNumber)
      }
    }
    
    // Start animation when element is visible
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          updateNumber()
          observer.unobserve(entry.target)
        }
      })
    })
    
    observer.observe(htmlElement)
  })
}

// Parallax effect for background elements
export const addParallaxEffect = (): void => {
  if (typeof window === 'undefined') return

  const parallaxElements = document.querySelectorAll('[data-parallax]')
  
  const handleScroll = () => {
    const scrolled = window.pageYOffset
    
    parallaxElements.forEach((element) => {
      const htmlElement = element as HTMLElement
      const rate = scrolled * -0.5
      htmlElement.style.transform = `translateY(${rate}px)`
    })
  }
  
  window.addEventListener('scroll', handleScroll, { passive: true })
}

// Typewriter effect for text
export const typewriterEffect = (selector: string, speed: number = 50): void => {
  if (typeof window === 'undefined') return

  const elements = document.querySelectorAll(selector)
  
  elements.forEach((element) => {
    const htmlElement = element as HTMLElement
    const text = htmlElement.textContent || ''
    htmlElement.textContent = ''
    
    let i = 0
    const typeWriter = () => {
      if (i < text.length) {
        htmlElement.textContent += text.charAt(i)
        i++
        setTimeout(typeWriter, speed)
      }
    }
    
    // Start typewriter when element is visible
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          typeWriter()
          observer.unobserve(entry.target)
        }
      })
    })
    
    observer.observe(htmlElement)
  })
}
