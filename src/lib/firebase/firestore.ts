import { collection, getDocs } from 'firebase/firestore';
import { getFirebaseInstances } from '../firebase-client';
import { Partner } from '@/types/partner';

export const getPartners = async (): Promise<Partner[]> => {
  try {
    const { db } = getFirebaseInstances();
    if (!db) {
      throw new Error('Firestore not initialized');
    }
    const querySnapshot = await getDocs(collection(db, 'partners'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Partner[];
  } catch (error) {
    console.error('Error fetching partners:', error);
    throw error;
  }
};
