import { ref, listAll, getDownloadURL } from 'firebase/storage';
import { getFirebaseInstances } from '../firebase-client';

export interface PartnerLogo {
  name: string;
  url: string;
}

export const listPartnerLogos = async (): Promise<PartnerLogo[]> => {
  try {
    console.log('Fetching partner logos from storage...');

    const { storage } = getFirebaseInstances();
    if (!storage) {
      console.error('Firebase Storage not initialized');
      return [];
    }

    // Create a reference to the partner folder in storage
    const partnerLogosRef = ref(storage, 'partner');
    
    // List all items in the partner folder
    const result = await listAll(partnerLogosRef);
    
    // Get download URLs for each logo
    const logoPromises = result.items.map(async (item) => {
      try {
        const url = await getDownloadURL(item);
        return {
          name: item.name.replace(/\.[^/.]+$/, ''), // Remove file extension
          url: url
        };
      } catch (error) {
        console.error(`Error getting URL for ${item.name}:`, error);
        return null;
      }
    });

    const logos = await Promise.all(logoPromises);
    // Filter out any failed logo fetches
    return logos.filter((logo): logo is PartnerLogo => logo !== null);
    
  } catch (error) {
    console.error('Error listing partner logos:', error);
    throw error;
  }
};
