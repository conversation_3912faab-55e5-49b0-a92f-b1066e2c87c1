import { ref, getDownloadURL } from 'firebase/storage';
import { getFirebaseInstances } from '../firebase-client';

export const getStorageUrl = async (path: string, folder: string): Promise<string> => {
  try {
    const { storage } = getFirebaseInstances();
    if (!storage) {
      throw new Error('Firebase Storage not initialized');
    }
    const storageRef = ref(storage, `${folder}/${path}`);
    return await getDownloadURL(storageRef);
  } catch (error) {
    console.error('Error getting storage URL:', error);
    throw error;
  }
};
