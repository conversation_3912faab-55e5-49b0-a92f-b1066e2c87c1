const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs } = require('firebase/firestore');

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function testFirestoreConnection() {
  try {
    console.log('Testing Firestore connection...');
    
    // Test reading from galleryImages collection
    const querySnapshot = await getDocs(collection(db, 'galleryImages'));
    console.log(`Successfully connected to Firestore. Found ${querySnapshot.size} documents in galleryImages.`);
    
    // Log the first document if available
    if (!querySnapshot.empty) {
      querySnapshot.forEach((doc) => {
        console.log('Document data:', doc.data());
      });
    }
    
  } catch (error) {
    console.error('Error testing Firestore connection:', error);
  }
}

testFirestoreConnection();
