import { GalleryImage } from './firebase';

// List of images in the public/Website Images directory
export const localGalleryImages = [
  {
    id: 'img-1',
    url: '/Website Images/ghana-landscape-aerial-view.png',
    title: 'Aerial View of Ghana',
    description: 'Beautiful aerial view of the Ghanaian landscape',
    category: 'Landscape',
    isFeatured: true
  },
  {
    id: 'img-2',
    url: '/Website Images/ghana-waterfalls-nature.png',
    title: 'Ghana Waterfalls',
    description: 'Scenic waterfalls in Ghana',
    category: 'Nature',
    isFeatured: true
  },
  {
    id: 'img-3',
    url: '/Website Images/ghana-scenic-landscape.png',
    title: 'Scenic Landscape',
    description: 'Beautiful Ghanaian countryside',
    category: 'Landscape',
    isFeatured: true
  },
  {
    id: 'img-4',
    url: '/Website Images/ghana-wildlife-viewing.png',
    title: 'Wildlife',
    description: 'Wildlife in Ghana',
    category: 'Wildlife',
    isFeatured: true
  },
  {
    id: 'img-5',
    url: '/Website Images/ghana_osuodumgya_otutu_obabio_v_leader.png',
    title: 'Royal Leader',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Otutu Obabio V',
    category: 'Royalty',
    isFeatured: true
  },
  {
    id: 'img-6',
    url: '/Website Images/ghana_mountain_landscape_1.png',
    title: 'Mountain View',
    description: 'Scenic mountain landscape',
    category: 'Landscape',
    isFeatured: true
  }
];

// Function to get local gallery images
export const getLocalGalleryImages = async (): Promise<GalleryImage[]> => {
  // Simulate async operation to match the Firebase function signature
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(localGalleryImages);
    }, 100);
  });
};
