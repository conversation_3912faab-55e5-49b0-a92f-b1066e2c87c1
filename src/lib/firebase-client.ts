'use client';

// This file ensures Firebase is only initialized on the client side
import { initializeApp, getApps, getApp, FirebaseApp } from 'firebase/app';
import { getFirestore, Firestore } from 'firebase/firestore';
import { getStorage, FirebaseStorage } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

let firebaseApp: FirebaseApp | null = null;
let db: Firestore | null = null;
let storage: FirebaseStorage | null = null;

// Initialize Firebase only on client side
export const initializeFirebaseClient = () => {
  if (typeof window === 'undefined') {
    console.warn('Firebase initialization attempted on server side');
    return { app: null, db: null, storage: null };
  }

  if (firebaseApp && db && storage) {
    return { app: firebaseApp, db, storage };
  }

  try {
    // Validate configuration
    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    const missingFields = requiredFields.filter(field => !firebaseConfig[field as keyof typeof firebaseConfig]);
    
    if (missingFields.length > 0) {
      throw new Error(`Missing Firebase configuration fields: ${missingFields.join(', ')}`);
    }

    if (!getApps().length) {
      firebaseApp = initializeApp(firebaseConfig);
      console.log('Firebase initialized successfully');
    } else {
      firebaseApp = getApp();
    }

    db = getFirestore(firebaseApp);
    storage = getStorage(firebaseApp);

    return { app: firebaseApp, db, storage };
  } catch (error) {
    console.error('Firebase initialization error:', error);
    return { app: null, db: null, storage: null };
  }
};

// Get Firebase instances (client-side only)
export const getFirebaseInstances = () => {
  if (typeof window === 'undefined') {
    return { app: null, db: null, storage: null };
  }
  
  if (!firebaseApp || !db || !storage) {
    return initializeFirebaseClient();
  }
  
  return { app: firebaseApp, db, storage };
};
