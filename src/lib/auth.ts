import { getAuth, onAuthStateChanged, User, signInWithEmailAndPassword, signOut, Auth } from 'firebase/auth';
import { getFirebaseInstances } from './firebase-client';

// Get auth instance dynamically
const getAuthInstance = (): Auth | null => {
  const { app } = getFirebaseInstances();
  if (!app) {
    console.error('Firebase app not initialized');
    return null;
  }
  return getAuth(app);
};

// Admin email that will have super admin privileges
const ADMIN_EMAIL = '<EMAIL>';

// Check if current user is admin
export const isAdmin = (user: User | null): boolean => {
  return user?.email === ADMIN_EMAIL;
};

// Sign in with email and password
export const signInAdmin = async (email: string, password: string) => {
  try {
    const auth = getAuthInstance();
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }

    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    if (!isAdmin(userCredential.user)) {
      await signOut(auth);
      throw new Error('Not authorized as admin');
    }
    return userCredential.user;
  } catch (error) {
    console.error('Error signing in:', error);
    throw error;
  }
};

// Sign out
export const signOutAdmin = async () => {
  try {
    const auth = getAuthInstance();
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }
    await signOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Auth state observer
export const onAuthStateChangedListener = (callback: (user: User | null) => void) => {
  const auth = getAuthInstance();
  if (!auth) {
    console.error('Firebase Auth not initialized');
    callback(null);
    return () => {}; // Return empty unsubscribe function
  }

  return onAuthStateChanged(auth, (user) => {
    if (user && !isAdmin(user)) {
      signOut(auth);
      callback(null);
    } else {
      callback(user);
    }
  });
};
