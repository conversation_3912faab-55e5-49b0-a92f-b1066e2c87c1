import { getAnalytics, isSupported, logEvent, setCurrentScreen, setUserId, setUserProperties } from 'firebase/analytics';
import { getAuth, onAuthStateChanged, User } from 'firebase/auth';
import { getApp } from 'firebase/app';

declare global {
  interface Window {
    gtag: any;
  }
}

export const trackPageView = (url: string, title: string) => {
  if (typeof window === 'undefined') return;
  
  // Log page view to Firebase Analytics
  isSupported().then(yes => {
    if (yes) {
      const analytics = getAnalytics();
      logEvent(analytics, 'page_view', {
        page_title: title,
        page_location: window.location.href,
        page_path: url,
        referrer: document.referrer || 'direct'
      });
    }
  });
};

export const trackEvent = (eventName: string, params: Record<string, any> = {}) => {
  if (typeof window === 'undefined') return;

  // Add default parameters
  const eventParams = {
    ...params,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    path: window.location.pathname,
    referrer: document.referrer || 'direct'
  };

  // Log event to Firebase Analytics
  isSupported().then(yes => {
    if (yes) {
      const analytics = getAnalytics();
      logEvent(analytics, eventName, eventParams);
    }
  });
};

export const trackUserEngagement = (contentType: string, itemId: string) => {
  trackEvent('user_engagement', {
    content_type: contentType,
    item_id: itemId,
    engagement_time_msec: 100 // Default engagement time
  });
};

export const trackUserLocation = () => {
  if (typeof window === 'undefined') return;

  // This will be populated by the browser's geolocation API if available
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        trackEvent('user_location', {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy
        });
      },
      (error) => {
        console.error('Error getting location:', error);
      },
      { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 }
    );
  }
};

export const trackDeviceInfo = () => {
  if (typeof window === 'undefined') return;

  const userAgent = navigator.userAgent;
  const screenResolution = `${window.screen.width}x${window.screen.height}`;
  const viewportSize = `${window.innerWidth}x${window.innerHeight}`;
  const language = navigator.language;
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  trackEvent('device_info', {
    user_agent: userAgent,
    screen_resolution: screenResolution,
    viewport_size: viewportSize,
    language: language,
    timezone: timezone,
    cookie_enabled: navigator.cookieEnabled,
    online: navigator.onLine,
    platform: navigator.platform
  });
};

// Initialize user tracking
export const initUserTracking = () => {
  if (typeof window === 'undefined') return;

  const auth = getAuth();
  
  onAuthStateChanged(auth, (user) => {
    if (user) {
      // Set user ID and properties
      isSupported().then(yes => {
        if (yes) {
          const analytics = getAnalytics();
          setUserId(analytics, user.uid);
          setUserProperties(analytics, {
            sign_up_method: user.providerData[0]?.providerId || 'unknown',
            email_verified: user.emailVerified ? 'verified' : 'unverified',
            account_created: user.metadata.creationTime || 'unknown',
            last_sign_in: user.metadata.lastSignInTime || 'never'
          });
        }
      });
    }
  });
};

// Track page load performance
export const trackPageLoad = () => {
  if (typeof window === 'undefined') return;

  window.addEventListener('load', () => {
    const timing = window.performance?.timing;
    if (!timing) return;

    const pageLoadTime = timing.loadEventEnd - timing.navigationStart;
    const dnsTime = timing.domainLookupEnd - timing.domainLookupStart;
    const tcpTime = timing.connectEnd - timing.connectStart;
    const serverResponseTime = timing.responseEnd - timing.requestStart;
    const domLoadTime = timing.domComplete - timing.domLoading;

    trackEvent('page_load', {
      page_load_time: pageLoadTime,
      dns_lookup_time: dnsTime,
      tcp_connection_time: tcpTime,
      server_response_time: serverResponseTime,
      dom_load_time: domLoadTime,
      page_url: window.location.href,
      page_title: document.title
    });
  });
};

// Track outbound links
export const trackOutboundLinks = () => {
  if (typeof window === 'undefined') return;

  document.body.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    const link = target.closest('a');
    
    if (link && link.href && !link.href.startsWith(window.location.origin)) {
      trackEvent('outbound_link_click', {
        link_url: link.href,
        link_text: link.innerText,
        source_url: window.location.href
      });
      
      // Small delay to allow the event to be sent
      setTimeout(() => {
        window.location.href = link.href;
      }, 150);
      
      event.preventDefault();
    }
  });
};
