'use client';

import { useEffect, useState } from 'react';
import { listPartnerLogos } from '@/lib/firebase/partnerLogos';

export default function PartnerLogosPage() {
  const [logos, setLogos] = useState<Array<{name: string, url: string}>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLogos = async () => {
      try {
        setLoading(true);
        const logoList = await listPartnerLogos();
        setLogos(logoList);
      } catch (err) {
        console.error('Error fetching logos:', err);
        setError('Failed to load partner logos. Please check the console for details.');
      } finally {
        setLoading(false);
      }
    };

    fetchLogos();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalGold mx-auto mb-4"></div>
          <p className="text-gray-600">Loading partner logos...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Error Loading Logos</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-royalGold text-white rounded-md hover:bg-yellow-600 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-center text-gray-900 mb-8">Partner Logos</h1>
        
        {logos.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No partner logos found in storage.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {logos.map((logo, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                <div className="h-40 bg-gray-100 rounded-md mb-3 flex items-center justify-center overflow-hidden">
                  <img 
                    src={logo.url} 
                    alt={logo.name}
                    className="max-h-full max-w-full object-contain p-2"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder-logo.png';
                    }}
                  />
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-700 break-words">{logo.name}</p>
                  <a 
                    href={logo.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-xs text-royalGold hover:underline mt-1 inline-block"
                  >
                    View Full Size
                  </a>
                </div>
              </div>
            ))}
          </div>
        )}
        
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-800 mb-2">Storage Path</h2>
          <code className="block bg-white p-2 rounded text-sm text-gray-700 overflow-x-auto">
            /partner/
          </code>
          <p className="mt-2 text-sm text-blue-700">
            Found {logos.length} logo{logos.length !== 1 ? 's' : ''} in storage.
          </p>
        </div>
      </div>
    </div>
  );
}
