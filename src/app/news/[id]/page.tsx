'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { getDoc, doc } from 'firebase/firestore';
import { getStorageUrl } from '@/lib/firebase';
import { getFirebaseInstances } from '@/lib/firebase-client';
import { format } from 'date-fns';
import Link from 'next/link';

import { NewsArticle as FirebaseNewsArticle } from '@/lib/firebase';

// Extend the Firebase NewsArticle with our additional fields
interface NewsArticle extends FirebaseNewsArticle {
  fullImageUrl?: string;
  excerpt: string; // Always defined with a default empty string
  summary?: string; // For backward compatibility
}

export default function NewsArticlePage() {
  const { id } = useParams();
  const [article, setArticle] = useState<NewsArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  useEffect(() => {
    const fetchArticle = async () => {
      if (!id) return;

      const { db } = getFirebaseInstances();
      if (!db) {
        setError('Database not initialized');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const docRef = doc(db, 'news', id as string);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const data = docSnap.data() as Omit<NewsArticle, 'id'>;
          // Create a complete NewsArticle object with all required fields
          const articleData: NewsArticle = {
            id: docSnap.id,
            title: data.title || 'Untitled',
            content: data.content || '',
            excerpt: data.excerpt || data.summary || '',
            category: data.category || 'general',
            date: data.date || new Date(),
            readTime: data.readTime || '2 min read',
            author: data.author || 'Admin',
            isFeatured: Boolean(data.isFeatured) || false,
            ...data // Spread the rest of the data
          };
          setArticle(articleData);
          
          // Fetch image URL if exists
          if (data.imageUrl) {
            try {
              const url = await getStorageUrl(data.imageUrl, 'news');
              setImageUrl(url);
            } catch (err) {
              console.error('Error loading image:', err);
            }
          }
        } else {
          setError('Article not found');
        }
      } catch (err) {
        console.error('Error fetching article:', err);
        setError('Failed to load article. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [id]);

  const formatDate = (date: any, formatType: 'short' | 'long' = 'short'): string => {
    try {
      const dateObj = date?.toDate ? date.toDate() : new Date(date);
      if (formatType === 'long') {
        return format(dateObj, 'EEEE, MMMM d, yyyy'); // e.g., "Monday, January 1, 2023"
      }
      return format(dateObj, 'MMMM d, yyyy'); // e.g., "January 1, 2023"
    } catch (err) {
      console.error('Error formatting date:', err);
      return '';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-royalGold"></div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="text-red-500 text-lg mb-4">{error || 'Article not found'}</div>
        <Link 
          href="/news" 
          className="px-6 py-2 bg-royalGold text-royalBlue rounded-lg font-medium hover:bg-yellow-500 transition-colors"
        >
          Back to News
        </Link>
      </div>
    );
  }

  return (
    <section className="py-20 relative overflow-hidden min-h-screen">
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/8 via-transparent to-royalBlue/8"></div>

      <div className="container mx-auto px-4 relative z-10 py-12">
        <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Back Button */}
          <div className="px-8 pt-8">
            <Link 
              href="/news" 
              className="inline-flex items-center text-royalGold hover:text-royalBlue transition-colors group"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-5 w-5 mr-2 transition-transform duration-200 group-hover:-translate-x-1" 
                viewBox="0 0 20 20" 
                fill="currentColor"
              >
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              <span className="font-medium">Back to News</span>
            </Link>
          </div>

          {/* Article Content */}
          <article className="p-8 pt-6">
            {/* Article Header */}
            <header className="mb-8">
              <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4">
                <span className="bg-royalGold/10 text-royalGold px-3 py-1 rounded-full text-xs font-medium mb-2 sm:mb-0">
                  {article.category || 'News'}
                </span>
                <span className="mx-3 hidden sm:inline-block">•</span>
                <span className="w-full sm:w-auto">{formatDate(article.date)}</span>
                {article.readTime && (
                  <>
                    <span className="mx-3 hidden sm:inline-block">•</span>
                    <span className="hidden sm:inline-block">{article.readTime}</span>
                  </>
                )}
                {article.author && (
                  <>
                    <span className="mx-3 hidden sm:inline-block">•</span>
                    <span className="w-full sm:w-auto">By {article.author}</span>
                  </>
                )}
              </div>
              
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-gray-900 mb-6 leading-tight">
                {article.title}
              </h1>
              
              {article.excerpt && (
                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                  {article.excerpt}
                </p>
              )}
            </header>

            {/* Featured Image */}
            {imageUrl && (
              <div className="mb-10 rounded-lg overflow-hidden shadow-lg">
                <img 
                  src={imageUrl} 
                  alt={article.title}
                  className="w-full h-auto max-h-[600px] object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              </div>
            )}
            
            {/* Article Body */}
            <div className="prose max-w-none text-gray-700 text-lg leading-relaxed">
              {article.content ? (
                <div dangerouslySetInnerHTML={{ __html: article.content }} />
              ) : (
                <p className="text-gray-500 italic">No content available for this article.</p>
              )}
            </div>
            
            {/* Article Footer */}
            <footer className="mt-12 pt-6 border-t border-gray-100">
              <div className="flex flex-wrap items-center justify-between">
                <div className="text-sm text-gray-500">
                  Published on {formatDate(article.date, 'long')}
                </div>
                <Link 
                  href="/news" 
                  className="inline-flex items-center text-royalGold hover:text-royalBlue transition-colors mt-4 sm:mt-0"
                >
                  <span className="mr-2">Back to News</span>
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    className="h-4 w-4" 
                    viewBox="0 0 20 20" 
                    fill="currentColor"
                  >
                    <path fillRule="evenodd" d="M10.293 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 13.586V3a1 1 0 112 0v10.586l4.293-4.293a1 1 0 011.414 1.414l-6 6z" clipRule="evenodd" />
                  </svg>
                </Link>
              </div>
            </footer>
          </article>
        </div>
      </div>
    </section>
  );
}
