'use client'

import dynamic from 'next/dynamic'
import Header from '../../components/layout/Header'
import Footer from '../../components/layout/Footer'

// Dynamically import the News component to prevent SSR issues
const News = dynamic(() => import('../../components/pages/News'), {
  ssr: false,
  loading: () => <div className="flex justify-center items-center min-h-[400px]">Loading news...</div>
});

export default function NewsPage() {
  return (
    <main className="min-h-screen">
      <Header />
      <div className="pt-20">
        <News />
        <Footer />
      </div>
    </main>
  )
}
