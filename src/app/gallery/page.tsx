'use client'

import dynamic from 'next/dynamic'
import Header from '../../components/layout/Header'
import Footer from '../../components/layout/Footer'

// Dynamically import the gallery page to prevent SSR issues
const RoyalGalleryPage = dynamic(() => import('../../components/pages/RoyalGalleryPage'), {
  ssr: false,
  loading: () => <div className="flex justify-center items-center min-h-[400px]">Loading gallery...</div>
});

export default function GalleryPage() {
  return (
    <main className="min-h-screen">
      <Header />
      <div>
        <RoyalGalleryPage />
        <Footer />
      </div>
    </main>
  )
}
