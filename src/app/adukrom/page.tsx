'use client'

import dynamic from 'next/dynamic'
import Header from '../../components/layout/Header'
import NewHero from '../../components/NewHero'
import AboutAllen from '../../components/AboutAllen'
import AboutAdukrom from '../../components/AboutAdukrom'
import RoyalFamily from '../../components/RoyalFamily'
import RoyalCoronation from '../../components/RoyalCoronation'
import Initiatives from '../../components/Initiatives'
import Contact from '../../components/Contact'
import Footer from '../../components/layout/Footer'

// Dynamically import Firebase-dependent components to prevent SSR issues
const StrategicPartners = dynamic(() => import('../../components/StrategicPartners'), {
  ssr: false,
  loading: () => <div className="flex justify-center items-center min-h-[200px]">Loading partners...</div>
});

const RoyalGallery = dynamic(() => import('../../components/RoyalGallery'), {
  ssr: false,
  loading: () => <div className="flex justify-center items-center min-h-[200px]">Loading gallery...</div>
});

export default function AdukromPage() {
  return (
    <main className="min-h-screen">
      <Header />
      <div className="pt-20">
        <NewHero />
      <AboutAllen />
      <AboutAdukrom />
      <RoyalFamily />
      <RoyalCoronation />
      <Initiatives />
      <StrategicPartners />
      <RoyalGallery />
        <Contact />
        <Footer />
      </div>
    </main>
  )
}
