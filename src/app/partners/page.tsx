'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { FiSearch, FiArrowRight } from 'react-icons/fi';
import { FaHandshake, FaEnvelope, FaExternalLinkAlt, FaSpinner } from 'react-icons/fa';
import { Partner } from '@/types/partner';
import { getPartners } from '@/lib/firebase/firestore';
import { getStorage, ref, getDownloadURL } from 'firebase/storage';
import Image from 'next/image';
import Link from 'next/link';
import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';

interface PartnerWithLogoUrl extends Partner {
  logoUrl?: string;
}

export default function PartnersPage() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [partners, setPartners] = useState<PartnerWithLogoUrl[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>('all');

  // Filter partners based on search term and category
  const filteredPartners = useCallback((): PartnerWithLogoUrl[] => {
    const searchLower = searchTerm.toLowerCase();
    return partners.filter(partner => {
      const nameMatch = partner.name?.toLowerCase().includes(searchLower) ?? false;
      const descMatch = partner.description?.toLowerCase().includes(searchLower) ?? false;
      const matchesSearch = nameMatch || descMatch;
      const matchesCategory = activeCategory === 'all' || partner.category === activeCategory;
      return matchesSearch && matchesCategory;
    });
  }, [partners, searchTerm, activeCategory]);

  // Get filtered and processed partners list
  const getProcessedPartners = useCallback((): PartnerWithLogoUrl[] => {
    const filtered = filteredPartners();
    return filtered
      .filter(partner => partner.name && partner.name.toLowerCase() !== 'partner')
      .slice(0, 12); // Increased number of partners to show
  }, [filteredPartners]);

  // Get unique categories from partners
  const categories = ['all', ...Array.from(new Set(partners.map(partner => partner.category).filter(Boolean)))] as string[];

  useEffect(() => {
    const fetchPartners = async () => {
      // Only run on client side
      if (typeof window === 'undefined') return;

      try {
        setLoading(true);
        setError(null);

        // Fetch partners from Firestore
        const partnersData = await getPartners();

        // Get logo URLs for each partner
        const storage = getStorage();
        const partnersWithLogos = await Promise.all(
          partnersData.map(async (partner) => {
            if (partner.logo) {
              try {
                const logoRef = ref(storage, `partner/${partner.logo}`);
                const logoUrl = await getDownloadURL(logoRef);
                return { ...partner, logoUrl };
              } catch (err) {
                console.error(`Error loading logo for ${partner.name}:`, err);
                return partner; // Return partner without logo if there's an error
              }
            }
            return partner;
          })
        );

        setPartners(partnersWithLogos);
      } catch (err) {
        console.error('Error fetching partners:', err);
        setError('Failed to load partners. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPartners();
  }, []);

  // Render loading state
  if (loading) {
    return (
      <main className="min-h-screen bg-gradient-to-b from-royalBlue to-royalNavy">
        <Header />
        <div className="pt-20 min-h-[60vh] flex items-center justify-center">
          <div className="flex flex-col items-center">
            <FaSpinner className="animate-spin text-4xl text-royalGold mb-4" />
            <p className="text-white">Loading partners...</p>
          </div>
        </div>
        <Footer />
      </main>
    );
  }

  // Render error state
  if (error) {
    return (
      <main className="min-h-screen bg-gradient-to-b from-royalBlue to-royalNavy">
        <Header />
        <div className="pt-20 min-h-[60vh] flex items-center justify-center">
          <div className="text-center p-8 bg-white/10 backdrop-blur-lg rounded-2xl max-w-md mx-4">
            <div className="text-royalGold text-5xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-white mb-2">Error Loading Partners</h2>
            <p className="text-white/80 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-royalGold text-royalBlue font-bold rounded-lg hover:bg-yellow-400 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
        <Footer />
      </main>
    );
  }

  // Get processed partners list
  const filteredPartnersList = getProcessedPartners();

  return (
    <main className="min-h-screen bg-gradient-to-b from-royalBlue to-royalNavy">
      <Header />
      
      {/* Hero Section */}
      <section className="relative pt-32 pb-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-grid-white/5 [mask-image:linear-gradient(to_bottom,transparent,black_20%,black_80%,transparent)]"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-royalBlue/80 to-royalNavy/80"></div>
        <div className="container mx-auto relative z-10 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            Strategic Partners
          </h1>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            We collaborate with industry leaders to deliver exceptional value and innovation.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link 
              href="/contact" 
              className="px-6 py-3 bg-royalGold text-royalBlue font-bold rounded-lg flex items-center gap-2 hover:bg-yellow-400 transition-colors"
            >
              Become a Partner <FaHandshake className="inline" />
            </Link>
            <Link 
              href="/contact" 
              className="px-6 py-3 bg-transparent border-2 border-white/30 text-white font-bold rounded-lg flex items-center gap-2 hover:bg-white/10 transition-colors"
            >
              Contact Us <FaEnvelope className="inline" />
            </Link>
          </div>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-8 px-4 bg-white/5 backdrop-blur-sm">
        <div className="container mx-auto max-w-5xl">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="relative w-full md:w-96">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search partners..."
                className="block w-full pl-10 pr-3 py-3 border border-white/20 bg-white/10 text-white placeholder-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-royalGold focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-wrap justify-center gap-2 w-full md:w-auto">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    activeCategory === category
                      ? 'bg-royalGold text-royalBlue'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Partners Grid */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-7xl">
          {filteredPartnersList.length === 0 ? (
            <div className="text-center py-20">
              <div className="text-6xl mb-6">🔍</div>
              <h3 className="text-2xl font-bold text-white mb-2">No partners found</h3>
              <p className="text-white/70 mb-6">Try adjusting your search or filter criteria</p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setActiveCategory('all');
                }}
                className="px-6 py-2 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition-colors"
              >
                Clear filters
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {filteredPartnersList.map((partner, index) => (
                <div 
                  key={partner.id || index} 
                  className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden hover:shadow-2xl hover:bg-white/10 transition-all duration-300 hover:-translate-y-1 flex flex-col"
                >
                  <div className="p-6 flex-1 flex flex-col items-center text-center">
                    {partner.logoUrl ? (
                      <div className="relative w-32 h-32 mb-6 flex items-center justify-center">
                        <div className="absolute inset-0 rounded-full border-2 border-royalGold/50 p-1">
                          <div className="w-full h-full rounded-full border-2 border-royalGold/30 p-1">
                            <div className="w-full h-full bg-white rounded-full flex items-center justify-center p-4">
                              <img 
                                src={partner.logoUrl} 
                                alt={partner.name} 
                                className="max-h-full max-w-full object-contain"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.onerror = null;
                                  target.src = '/placeholder-logo.png';
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="relative w-32 h-32 mb-6">
                        <div className="absolute inset-0 rounded-full border-2 border-royalGold/50 p-1">
                          <div className="w-full h-full rounded-full border-2 border-royalGold/30 p-1">
                            <div className="w-full h-full bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center text-4xl text-royalBlue font-bold">
                              {partner.name?.charAt(0) || 'P'}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <h3 className="text-xl font-bold text-white mb-2">{partner.name}</h3>
                    
                    {partner.description && (
                      <p className="text-white/70 text-sm mb-4 line-clamp-3">
                        {partner.description}
                      </p>
                    )}

                    {partner.website && (
                      <div className="mt-auto w-full">
                        <a
                          href={partner.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-royalGold hover:text-yellow-400 text-sm font-medium mt-4 transition-colors"
                        >
                          Visit Website <FaExternalLinkAlt className="ml-1" size={12} />
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-royalBlue/80 to-royalNavy/80">
        <div className="container mx-auto max-w-4xl text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Become a Partner</h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join our network of strategic partners and grow your business with us.
          </p>
          <Link 
            href="/contact" 
            className="inline-flex items-center px-8 py-4 bg-royalGold text-royalBlue font-bold rounded-xl hover:bg-yellow-400 transition-all duration-300 text-lg"
          >
            Get in Touch <FiArrowRight className="ml-2" />
          </Link>
        </div>
      </section>

      <Footer />
    </main>
  );
}
