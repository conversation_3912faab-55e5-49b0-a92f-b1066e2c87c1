import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

// Email configuration mapping
const getEmailConfig = (type: string) => {
  switch (type) {
    case 'contact':
      return {
        from: process.env.EVENTS_EMAIL || '<EMAIL>',
        password: process.env.EVENTS_PASSWORD,
        to: process.env.INFO_EMAIL || '<EMAIL>'
      }
    case 'newsletter':
      return {
        from: process.env.EVENTS_EMAIL || '<EMAIL>',
        password: process.env.EVENTS_PASSWORD,
        to: process.env.INFO_EMAIL || '<EMAIL>'
      }
    case 'legal':
      return {
        from: process.env.LEGAL_EMAIL || '<EMAIL>',
        password: process.env.LEGAL_PASSWORD,
        to: process.env.LEGAL_EMAIL || '<EMAIL>'
      }
    case 'merchandise':
      return {
        from: process.env.MERCH_EMAIL || '<EMAIL>',
        password: process.env.MERCH_PASSWORD,
        to: process.env.INFO_EMAIL || '<EMAIL>'
      }
    default:
      return {
        from: process.env.EVENTS_EMAIL || '<EMAIL>',
        password: process.env.EVENTS_PASSWORD,
        to: process.env.INFO_EMAIL || '<EMAIL>'
      }
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, subject, message, type = 'contact' } = body

    console.log('Email API called with:', { name, email, subject, type })

    // Get email configuration based on type
    const emailConfig = getEmailConfig(type)

    // Check if email configuration is set
    if (!emailConfig.password) {
      console.error(`Email password not configured for type: ${type}`)
      return NextResponse.json(
        { error: 'Email configuration error' },
        { status: 500 }
      )
    }

    // Create transporter using the provided SMTP settings
    const transporter = nodemailer.createTransport({
      host: 's4478.usc1.stableserver.net',
      port: 465,
      secure: true, // true for 465, false for other ports
      auth: {
        user: emailConfig.from,
        pass: emailConfig.password,
      },
    })

    console.log(`Email configuration: FROM ${emailConfig.from} TO ${emailConfig.to}`)

    // Email content based on type
    let emailSubject = ''
    let emailHtml = ''

    if (type === 'contact') {
      emailSubject = `Contact Form: ${subject}`
      emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #1e3a8a, #3b82f6); padding: 20px; text-align: center;">
            <h1 style="color: #ffd700; margin: 0;">Adukrom Kingdom</h1>
            <p style="color: white; margin: 5px 0;">New Contact Form Submission</p>
          </div>
          
          <div style="padding: 20px; background: #f8fafc;">
            <h2 style="color: #1e3a8a; border-bottom: 2px solid #ffd700; padding-bottom: 10px;">Contact Details</h2>
            
            <div style="margin: 20px 0;">
              <strong style="color: #1e3a8a;">Name:</strong> ${name}
            </div>
            
            <div style="margin: 20px 0;">
              <strong style="color: #1e3a8a;">Email:</strong> ${email}
            </div>
            
            <div style="margin: 20px 0;">
              <strong style="color: #1e3a8a;">Subject:</strong> ${subject}
            </div>
            
            <div style="margin: 20px 0;">
              <strong style="color: #1e3a8a;">Message:</strong>
              <div style="background: white; padding: 15px; border-left: 4px solid #ffd700; margin-top: 10px;">
                ${message.replace(/\n/g, '<br>')}
              </div>
            </div>
          </div>
          
          <div style="background: #1e3a8a; padding: 15px; text-align: center;">
            <p style="color: #ffd700; margin: 0; font-size: 14px;">
              © 2025 Adukrom Kingdom. All rights reserved.
            </p>
          </div>
        </div>
      `
    } else if (type === 'newsletter') {
      emailSubject = 'New Newsletter Subscription'
      emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #1e3a8a, #3b82f6); padding: 20px; text-align: center;">
            <h1 style="color: #ffd700; margin: 0;">Adukrom Kingdom</h1>
            <p style="color: white; margin: 5px 0;">New Newsletter Subscription</p>
          </div>
          
          <div style="padding: 20px; background: #f8fafc;">
            <h2 style="color: #1e3a8a; border-bottom: 2px solid #ffd700; padding-bottom: 10px;">Subscription Details</h2>
            
            <div style="margin: 20px 0;">
              <strong style="color: #1e3a8a;">Email:</strong> ${email}
            </div>
            
            <div style="margin: 20px 0;">
              <strong style="color: #1e3a8a;">Subscription Date:</strong> ${new Date().toLocaleDateString()}
            </div>
          </div>
          
          <div style="background: #1e3a8a; padding: 15px; text-align: center;">
            <p style="color: #ffd700; margin: 0; font-size: 14px;">
              © 2025 Adukrom Kingdom. All rights reserved.
            </p>
          </div>
        </div>
      `
    }

    // Send email
    console.log('Attempting to send email...')
    const info = await transporter.sendMail({
      from: `"Adukrom Kingdom" <${emailConfig.from}>`,
      to: emailConfig.to,
      subject: emailSubject,
      html: emailHtml,
    })

    console.log('Email sent successfully: %s', info.messageId)

    return NextResponse.json(
      { message: 'Email sent successfully', messageId: info.messageId },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error sending email:', error)
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    )
  }
}
