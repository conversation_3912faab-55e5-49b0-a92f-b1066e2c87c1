import './globals.css'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Adukrom Kingdom - The Crown of Africa: Rise of a New Era',
  description: 'Official royal website of His Royal Majesty King <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> of Adukrom. Discover the heritage, initiatives, and vision that shape our kingdom\'s future.',
  keywords: 'Aduk<PERSON>, Ghana, King <PERSON>, <PERSON>, Majesty, Coronation, Heritage, Culture, Mpuntuhene, Eastern Region',
}

interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
        />
      </head>
      <body className="font-sans bg-cream">
        {children}
      </body>
    </html>
  )
}
