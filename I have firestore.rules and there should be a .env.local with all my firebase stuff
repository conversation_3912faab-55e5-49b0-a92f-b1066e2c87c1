rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Function to check if user is admin or super admin
    function isAdmin() {
      return request.auth != null && 
        (request.auth.token.superAdmin == true || 
         request.auth.token.admin == true ||
         request.auth.token.role in ['admin', 'super_admin']);
    }

    // Function to check if user is super admin
    function isSuperAdmin() {
      return request.auth != null && 
        (request.auth.token.superAdmin == true || 
         request.auth.token.role == 'super_admin');
    }

    // Development mode - only allow access if authenticated
    // match /{document=**} {
    //   allow read, write: if request.auth != null;
    // }
    
    // User profiles: only the user can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == userId || isAdmin());
    }

    // Admin users: only super admins can manage
    match /adminUser/{adminId} {
      allow read, write: if isAdmin();
    }

    // Public content: anyone can read, only admins can write
    match /news/{docId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Gallery: public read, admin write
    match /gallery/{docId} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }
    
    // Partners: public read, admin write
    match /partners/{docId} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }
    
    // RSVPs: public read/create, admin update/delete
    match /rsvps/{docId} {
      allow read, create: if true;
      allow update, delete: if isAdmin();
    }
    
    // Default deny all other access
    match /{document=**} {
      allow read, write: if isAdmin();
    }
  }
}